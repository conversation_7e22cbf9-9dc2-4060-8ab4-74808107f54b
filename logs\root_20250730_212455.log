2025-07-30 21:24:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-30 21:24:57 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\multi_model_01_updated\output\data_exploration
2025-07-30 21:24:57 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-30 21:24:57 - GUI - INFO - GUI界面初始化完成
2025-07-30 21:25:10 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-07-30 21:25:10 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-30 21:25:10 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 133, 'max_depth': 9, 'min_samples_split': 14, 'min_samples_leaf': 9, 'max_features': 'sqrt'}
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-07-30 21:25:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_212538.html
2025-07-30 21:25:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_212538.html
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 27.89 秒
2025-07-30 21:25:52 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-07-30 21:25:52 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-30 21:25:52 - model_training - INFO - 模型名称: Random Forest
2025-07-30 21:25:52 - model_training - INFO - 准确率: 0.8250
2025-07-30 21:25:52 - model_training - INFO - AUC: 0.9514
2025-07-30 21:25:52 - model_training - INFO - 混淆矩阵:
2025-07-30 21:25:52 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-07-30 21:25:52 - model_training - INFO - 
分类报告:
2025-07-30 21:25:52 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-07-30 21:25:52 - model_training - INFO - 训练时间: 0.08 秒
2025-07-30 21:25:52 - model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-30 21:25:52 - model_training - INFO - 特征名称已缓存到: d:\Code\multi_model_01_updated\cache\RandomForest_feature_names.joblib
