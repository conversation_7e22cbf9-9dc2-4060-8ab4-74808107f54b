2025-07-30 22:08:33 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-30 22:08:33 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\multi_model_01_updated\output\data_exploration
2025-07-30 22:08:33 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-30 22:08:33 - GUI - INFO - GUI界面初始化完成
2025-07-30 22:08:54 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-30 22:08:54 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-30 22:08:54 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 199, 'max_depth': 7, 'min_samples_split': 11, 'min_samples_leaf': 3, 'max_features': 'log2'}
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8411
2025-07-30 22:09:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 22:09:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_220928.html
2025-07-30 22:09:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 22:09:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_220928.html
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 34.69 秒
2025-07-30 22:09:42 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-07-30 22:09:42 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-30 22:09:42 - model_training - INFO - 模型名称: Random Forest
2025-07-30 22:09:42 - model_training - INFO - 准确率: 0.7250
2025-07-30 22:09:42 - model_training - INFO - AUC: 0.8184
2025-07-30 22:09:42 - model_training - INFO - 混淆矩阵:
2025-07-30 22:09:42 - model_training - INFO - 
[[19  4]
 [ 7 10]]
2025-07-30 22:09:42 - model_training - INFO - 
分类报告:
2025-07-30 22:09:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.83      0.78        23
           1       0.71      0.59      0.65        17

    accuracy                           0.72        40
   macro avg       0.72      0.71      0.71        40
weighted avg       0.72      0.72      0.72        40

2025-07-30 22:09:42 - model_training - INFO - 训练时间: 0.09 秒
2025-07-30 22:09:42 - model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\multi_model_01_updated\cache\RandomForest_results.joblib
2025-07-30 22:09:42 - model_training - INFO - 特征名称已缓存到: d:\Code\multi_model_01_updated\cache\RandomForest_feature_names.joblib
