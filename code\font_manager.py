#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一字体管理模块
解决matplotlib中文字体显示问题，避免字体设置冲突
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
import platform
import warnings
from pathlib import Path

# 抑制字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

class FontManager:
    """字体管理器 - 彻底解决字体问题"""

    def __init__(self):
        self.is_initialized = False
        self.current_font = None
        self.use_english_only = False  # 强制使用英文
        self.fallback_fonts = [
            'DejaVu Sans',
            'Arial',
            'Helvetica',
            'Liberation Sans',
            'sans-serif'
        ]
        
    def initialize_fonts(self):
        """初始化字体设置 - 彻底解决字体问题"""
        if self.is_initialized:
            return

        try:
            # 完全重置matplotlib参数
            plt.rcParams.update(plt.rcParamsDefault)

            # 允许显示所有字符，包括特殊字符和中文
            self.use_english_only = False

            # 设置支持特殊字符的字体配置
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = self.fallback_fonts + ['DejaVu Math TeX Gyre', 'STIX', 'STIXGeneral']
            plt.rcParams['axes.unicode_minus'] = True
            plt.rcParams['mathtext.fontset'] = 'stix'  # 设置数学字体为STIX，支持更多特殊字符
            plt.rcParams['figure.max_open_warning'] = 0

            # 禁用字体缓存
            plt.rcParams['font.size'] = 10
            plt.rcParams['axes.titlesize'] = 12
            plt.rcParams['axes.labelsize'] = 10
            plt.rcParams['xtick.labelsize'] = 9
            plt.rcParams['ytick.labelsize'] = 9
            plt.rcParams['legend.fontsize'] = 9

            # 设置绘图样式
            self._set_safe_plot_style()

            self.current_font = 'sans-serif'
            self.is_initialized = True

            print("✅ 完整字体模式已启用（支持所有字符）")

        except Exception as e:
            print(f"❌ 字体初始化失败: {e}")
            self._emergency_fallback()
    
    # 删除所有中文字体相关代码，简化为英文字体管理
    
    def _set_safe_plot_style(self):
        """设置安全的绘图样式"""
        try:
            # 不使用seaborn，避免额外的字体问题
            plt.style.use('default')

            # 手动设置网格样式
            plt.rcParams['axes.grid'] = True
            plt.rcParams['grid.alpha'] = 0.3
            plt.rcParams['axes.axisbelow'] = True

        except Exception as e:
            print(f"样式设置失败: {e}")
            pass
    
    def _emergency_fallback(self):
        """紧急备用方案 - 保留所有字符显示功能的基本配置"""
        try:
            # 完全重置到默认状态
            plt.rcParams.update(plt.rcParamsDefault)

            # 使用基本的字体设置，但保留特殊字符支持
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'STIX', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = True
            plt.rcParams['mathtext.fontset'] = 'stix'

            self.current_font = 'sans-serif'
            self.use_english_only = False  # 不强制使用英文，保留特殊字符
            self.is_initialized = True

            print("⚠️ 使用紧急字体配置（保留特殊字符支持）")

        except Exception:
            # 如果连这个都失败，保持特殊字符支持
            self.use_english_only = False
            self.is_initialized = True
    
    def get_safe_title(self, chinese_text, english_text):
        """获取安全的标题文本 - 允许所有字符显示"""
        # 优先使用中文，但如果强制英文模式则使用英文
        if self.use_english_only:
            return english_text
        return chinese_text

    def get_safe_label(self, chinese_text, english_text):
        """获取安全的标签文本 - 允许所有字符显示"""
        # 优先使用中文，但如果强制英文模式则使用英文
        if self.use_english_only:
            return english_text
        return chinese_text

# 全局字体管理器实例
_font_manager = FontManager()

def initialize_fonts():
    """初始化字体（全局函数）"""
    _font_manager.initialize_fonts()

def get_safe_title(chinese_text, english_text):
    """获取安全的标题文本（全局函数）"""
    if not _font_manager.is_initialized:
        _font_manager.initialize_fonts()
    return _font_manager.get_safe_title(chinese_text, english_text)

def get_safe_label(chinese_text, english_text):
    """获取安全的标签文本（全局函数）"""
    if not _font_manager.is_initialized:
        _font_manager.initialize_fonts()
    return _font_manager.get_safe_label(chinese_text, english_text)

def safe_text_display(chinese_text, english_text):
    """安全的文本显示（别名）"""
    return get_safe_title(chinese_text, english_text)

# 自动初始化
initialize_fonts()

# 导出常用函数
__all__ = [
    'initialize_fonts',
    'get_safe_title', 
    'get_safe_label',
    'safe_text_display'
]
