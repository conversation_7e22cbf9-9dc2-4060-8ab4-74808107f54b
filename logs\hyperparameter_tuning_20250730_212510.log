2025-07-30 21:25:10 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 133, 'max_depth': 9, 'min_samples_split': 14, 'min_samples_leaf': 9, 'max_features': 'sqrt'}
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-07-30 21:25:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_212538.html
2025-07-30 21:25:38 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_212538.html
2025-07-30 21:25:38 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 27.89 秒
