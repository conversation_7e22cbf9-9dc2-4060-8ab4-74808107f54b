2025-07-30 20:56:23 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-07-30 20:56:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 144, 'max_depth': 30, 'min_samples_split': 4, 'min_samples_leaf': 1, 'max_features': 'log2'}
2025-07-30 20:56:59 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9893
2025-07-30 20:56:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 20:56:59 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_205659.html
2025-07-30 20:56:59 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 20:56:59 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_205659.html
2025-07-30 20:56:59 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 36.63 秒
