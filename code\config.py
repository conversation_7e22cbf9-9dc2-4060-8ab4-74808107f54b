#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目配置文件
集中管理所有配置参数，避免硬编码和重复定义
"""

import os
from pathlib import Path
import matplotlib.font_manager as fm
import platform

# 基础路径配置
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_PATH = PROJECT_ROOT / 'data'
OUTPUT_PATH = PROJECT_ROOT / 'output'
CACHE_PATH = PROJECT_ROOT / 'cache'
MODELS_PATH = PROJECT_ROOT / 'models'
ENSEMBLE_PATH = PROJECT_ROOT / 'ensemble'
MULTI_DATA_CACHE_PATH = PROJECT_ROOT / 'multi_data_cache'
LOG_PATH = PROJECT_ROOT / 'logs'

# 自动创建必要的目录
for path in [DATA_PATH, OUTPUT_PATH, CACHE_PATH, MODELS_PATH, ENSEMBLE_PATH, MULTI_DATA_CACHE_PATH, LOG_PATH]:
    path.mkdir(parents=True, exist_ok=True)

# 随机种子设置
RANDOM_SEED = 42

# 数据预处理配置
DATA_CONFIG = {
    'test_size': 0.2,
    'random_state': RANDOM_SEED,
    'default_scaling': 'standard',
    'feature_selection_threshold': 0.01,
    'feature_selection_k': 10,
    'cv_folds': 5
}

# 模型训练配置
TRAINING_CONFIG = {
    'cv_folds': 5,
    'n_jobs': -1,
    'verbose': 1
}

# 模型超参数网格
HYPERPARAMETER_GRIDS = {
    'DecisionTree': {
        'max_depth': [3, 5, 7, 10, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'criterion': ['gini', 'entropy']
    },
    'RandomForest': {
        'n_estimators': [50, 100, 200],
        'max_depth': [5, 10, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'XGBoost': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'max_depth': [3, 5, 7],
        'min_child_weight': [1, 3, 5],
        'subsample': [0.7, 0.8, 0.9]
    },
    'LightGBM': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'max_depth': [3, 5, 7, -1],
        'num_leaves': [31, 50, 100],
        'subsample': [0.7, 0.8, 0.9]
    },
    'CatBoost': {
        'iterations': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.3],
        'depth': [4, 6, 8],
        'l2_leaf_reg': [1, 3, 5, 7]
    },
    'Logistic': {
        'C': [0.001, 0.01, 0.1, 1, 10, 100],
        'penalty': ['l1', 'l2', 'elasticnet', None],
        'solver': ['newton-cg', 'lbfgs', 'liblinear', 'sag', 'saga']
    },
    'SVM': {
        'C': [0.1, 1, 10, 100],
        'gamma': ['scale', 'auto', 0.1, 0.01],
        'kernel': ['rbf', 'linear', 'poly', 'sigmoid']
    },
    'KNN': {
        'n_neighbors': [3, 5, 7, 9, 11],
        'weights': ['uniform', 'distance'],
        'algorithm': ['auto', 'ball_tree', 'kd_tree', 'brute'],
        'p': [1, 2]
    },
    'NaiveBayes': {
        'var_smoothing': [1e-9, 1e-8, 1e-7, 1e-6]
    },
    'NeuralNet': {
        'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
        'activation': ['relu', 'tanh', 'logistic'],
        'alpha': [0.0001, 0.001, 0.01],
        'learning_rate': ['constant', 'adaptive', 'invscaling']
    }
}

# Optuna超参数搜索空间配置
# 为不同数据集大小定义不同的参数范围
OPTUNA_PARAM_SPACE = {
    # 默认参数范围 - 适用于中等大小的数据集
    'default': {
        'DecisionTree': {
            'max_depth': {'type': 'int', 'low': 3, 'high': 15},
            'min_samples_split': {'type': 'int', 'low': 2, 'high': 20},
            'min_samples_leaf': {'type': 'int', 'low': 1, 'high': 10},
            'criterion': {'type': 'categorical', 'choices': ['gini', 'entropy']},
            'class_weight': {'type': 'categorical', 'choices': [None, 'balanced']},
            'max_features': {'type': 'categorical', 'choices': [None, 'sqrt', 'log2']}
        },
        'RandomForest': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 20},
            'min_samples_split': {'type': 'int', 'low': 2, 'high': 20},
            'min_samples_leaf': {'type': 'int', 'low': 1, 'high': 10},
            'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2']}
        },
        'XGBoost': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 10},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'subsample': {'type': 'float', 'low': 0.5, 'high': 1.0},
            'colsample_bytree': {'type': 'float', 'low': 0.5, 'high': 1.0}
        },
        'LightGBM': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 12},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'feature_fraction': {'type': 'float', 'low': 0.5, 'high': 1.0},
            'bagging_fraction': {'type': 'float', 'low': 0.5, 'high': 1.0}
        },
        'CatBoost': {
            'iterations': {'type': 'int', 'low': 50, 'high': 300},
            'depth': {'type': 'int', 'low': 3, 'high': 10},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'l2_leaf_reg': {'type': 'float', 'low': 1.0, 'high': 10.0},
            'bagging_temperature': {'type': 'float', 'low': 0.0, 'high': 1.0}
        },
        'Logistic': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'solver': {'type': 'categorical', 'choices': ['lbfgs', 'liblinear']}
        },
        'SVM': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'kernel': {'type': 'categorical', 'choices': ['rbf', 'linear']}
        },
        'KNN': {
            'n_neighbors': {'type': 'int', 'low': 3, 'high': 15}
        },
        'NeuralNet': {
            'hidden_layer_sizes': {'type': 'categorical', 'choices': [(50,), (100,), (50, 50)]},
            'alpha': {'type': 'float', 'low': 0.0001, 'high': 0.01}
        },
        'NaiveBayes': {
            'var_smoothing': {'type': 'float', 'low': 1e-11, 'high': 1e-5, 'log': True}
        }
    },
    
    # 小数据集参数范围 - 适用于样本数小于1000的数据集
    'small': {
        'DecisionTree': {
            'max_depth': {'type': 'int', 'low': 2, 'high': 8},
            'min_samples_split': {'type': 'int', 'low': 5, 'high': 30},
            'min_samples_leaf': {'type': 'int', 'low': 3, 'high': 15},
            'criterion': {'type': 'categorical', 'choices': ['gini', 'entropy']},
            'class_weight': {'type': 'categorical', 'choices': [None, 'balanced']},
            'max_features': {'type': 'categorical', 'choices': [None, 'sqrt', 'log2']}
        },
        'RandomForest': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 200},
            'max_depth': {'type': 'int', 'low': 2, 'high': 10},
            'min_samples_split': {'type': 'int', 'low': 5, 'high': 20},
            'min_samples_leaf': {'type': 'int', 'low': 3, 'high': 15},
            'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2']}
        },
        'XGBoost': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 200},
            'max_depth': {'type': 'int', 'low': 2, 'high': 6},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.2},
            'subsample': {'type': 'float', 'low': 0.6, 'high': 1.0},
            'colsample_bytree': {'type': 'float', 'low': 0.6, 'high': 1.0},
            'reg_alpha': {'type': 'float', 'low': 0.0, 'high': 1.0},
            'reg_lambda': {'type': 'float', 'low': 0.5, 'high': 2.0}
        },
        'LightGBM': {
            'n_estimators': {'type': 'int', 'low': 50, 'high': 200},
            'max_depth': {'type': 'int', 'low': 2, 'high': 6},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.2},
            'feature_fraction': {'type': 'float', 'low': 0.6, 'high': 1.0},
            'bagging_fraction': {'type': 'float', 'low': 0.6, 'high': 1.0},
            'reg_alpha': {'type': 'float', 'low': 0.0, 'high': 1.0},
            'reg_lambda': {'type': 'float', 'low': 0.5, 'high': 2.0}
        },
        'CatBoost': {
            'iterations': {'type': 'int', 'low': 50, 'high': 200},
            'depth': {'type': 'int', 'low': 2, 'high': 6},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.2},
            'l2_leaf_reg': {'type': 'float', 'low': 1.0, 'high': 5.0},
            'bagging_temperature': {'type': 'float', 'low': 0.0, 'high': 0.5}
        },
        'Logistic': {
            'C': {'type': 'float', 'low': 0.1, 'high': 5.0},
            'solver': {'type': 'categorical', 'choices': ['lbfgs', 'liblinear']},
            'class_weight': {'type': 'categorical', 'choices': [None, 'balanced']}
        },
        'SVM': {
            'C': {'type': 'float', 'low': 0.1, 'high': 5.0},
            'kernel': {'type': 'categorical', 'choices': ['rbf', 'linear']},
            'class_weight': {'type': 'categorical', 'choices': [None, 'balanced']}
        },
        'KNN': {
            'n_neighbors': {'type': 'int', 'low': 3, 'high': 10},
            'weights': {'type': 'categorical', 'choices': ['uniform', 'distance']}
        },
        'NeuralNet': {
            'hidden_layer_sizes': {'type': 'categorical', 'choices': [(10,), (20,), (10, 5)]},
            'alpha': {'type': 'float', 'low': 0.001, 'high': 0.1},
            'learning_rate_init': {'type': 'float', 'low': 0.001, 'high': 0.01}
        },
        'NaiveBayes': {
            'var_smoothing': {'type': 'float', 'low': 1e-10, 'high': 1e-4, 'log': True}
        }
    },
    
    # 大数据集参数范围 - 适用于样本数大于10000的数据集
    'large': {
        'DecisionTree': {
            'max_depth': {'type': 'int', 'low': 5, 'high': 20},
            'min_samples_split': {'type': 'int', 'low': 10, 'high': 100},
            'min_samples_leaf': {'type': 'int', 'low': 5, 'high': 50},
            'criterion': {'type': 'categorical', 'choices': ['gini', 'entropy']},
            'max_features': {'type': 'categorical', 'choices': [None, 'sqrt', 'log2']}
        },
        'RandomForest': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 500},
            'max_depth': {'type': 'int', 'low': 5, 'high': 30},
            'min_samples_split': {'type': 'int', 'low': 10, 'high': 100},
            'min_samples_leaf': {'type': 'int', 'low': 5, 'high': 50},
            'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2']}
        },
        'XGBoost': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 500},
            'max_depth': {'type': 'int', 'low': 5, 'high': 15},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'subsample': {'type': 'float', 'low': 0.5, 'high': 1.0},
            'colsample_bytree': {'type': 'float', 'low': 0.5, 'high': 1.0},
            'tree_method': {'type': 'categorical', 'choices': ['hist', 'approx']}
        },
        'LightGBM': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 500},
            'max_depth': {'type': 'int', 'low': 5, 'high': 15},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'feature_fraction': {'type': 'float', 'low': 0.5, 'high': 1.0},
            'bagging_fraction': {'type': 'float', 'low': 0.5, 'high': 1.0}
        },
        'CatBoost': {
            'iterations': {'type': 'int', 'low': 100, 'high': 500},
            'depth': {'type': 'int', 'low': 5, 'high': 12},
            'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3},
            'l2_leaf_reg': {'type': 'float', 'low': 1.0, 'high': 10.0},
            'bagging_temperature': {'type': 'float', 'low': 0.0, 'high': 1.0}
        },
        'Logistic': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'solver': {'type': 'categorical', 'choices': ['saga', 'lbfgs']},
            'max_iter': {'type': 'int', 'low': 1000, 'high': 5000}
        },
        'SVM': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'kernel': {'type': 'categorical', 'choices': ['rbf', 'linear']},
            'probability': {'type': 'categorical', 'choices': [True]},
            'cache_size': {'type': 'int', 'low': 1000, 'high': 5000}
        },
        'KNN': {
            'n_neighbors': {'type': 'int', 'low': 5, 'high': 20},
            'algorithm': {'type': 'categorical', 'choices': ['ball_tree', 'kd_tree', 'auto']},
            'leaf_size': {'type': 'int', 'low': 20, 'high': 50}
        },
        'NeuralNet': {
            'hidden_layer_sizes': {'type': 'categorical', 'choices': [(100,), (200,), (100, 50), (200, 100)]},
            'alpha': {'type': 'float', 'low': 0.0001, 'high': 0.01},
            'batch_size': {'type': 'int', 'low': 100, 'high': 500},
            'learning_rate_init': {'type': 'float', 'low': 0.0001, 'high': 0.01}
        },
        'NaiveBayes': {
            'var_smoothing': {'type': 'float', 'low': 1e-12, 'high': 1e-6, 'log': True}
        }
    },
    
    # 高维数据集参数范围 - 适用于特征数量大于100的数据集
    'high_dim': {
        'DecisionTree': {
            'max_depth': {'type': 'int', 'low': 3, 'high': 12},
            'min_samples_split': {'type': 'int', 'low': 5, 'high': 30},
            'min_samples_leaf': {'type': 'int', 'low': 3, 'high': 15},
            'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2']}
        },
        'RandomForest': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 15},
            'max_features': {'type': 'categorical', 'choices': ['sqrt', 'log2']}
        },
        'XGBoost': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 10},
            'colsample_bytree': {'type': 'float', 'low': 0.3, 'high': 0.8},
            'colsample_bylevel': {'type': 'float', 'low': 0.3, 'high': 0.8},
            'colsample_bynode': {'type': 'float', 'low': 0.3, 'high': 0.8}
        },
        'LightGBM': {
            'n_estimators': {'type': 'int', 'low': 100, 'high': 300},
            'max_depth': {'type': 'int', 'low': 3, 'high': 10},
            'feature_fraction': {'type': 'float', 'low': 0.3, 'high': 0.8}
        },
        'CatBoost': {
            'iterations': {'type': 'int', 'low': 100, 'high': 300},
            'depth': {'type': 'int', 'low': 3, 'high': 10},
            'rsm': {'type': 'float', 'low': 0.3, 'high': 0.8}  # 随机子空间方法，类似于feature_fraction
        },
        'Logistic': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'penalty': {'type': 'categorical', 'choices': ['l1', 'l2', 'elasticnet']},
            'solver': {'type': 'categorical', 'choices': ['saga']},
            'l1_ratio': {'type': 'float', 'low': 0.1, 'high': 0.9}
        },
        'SVM': {
            'C': {'type': 'float', 'low': 0.1, 'high': 10.0},
            'kernel': {'type': 'categorical', 'choices': ['linear', 'rbf']},
            'gamma': {'type': 'categorical', 'choices': ['scale', 'auto']}
        },
        'NaiveBayes': {
            'var_smoothing': {'type': 'float', 'low': 1e-10, 'high': 1e-3, 'log': True}
        }
    },
    
    # 不平衡数据集参数范围 - 适用于类别不平衡的数据集
    'imbalanced': {
        'DecisionTree': {
            'class_weight': {'type': 'categorical', 'choices': ['balanced', None]},
            'min_samples_leaf': {'type': 'int', 'low': 5, 'high': 30}
        },
        'RandomForest': {
            'class_weight': {'type': 'categorical', 'choices': ['balanced', 'balanced_subsample', None]},
            'min_samples_leaf': {'type': 'int', 'low': 5, 'high': 30}
        },
        'XGBoost': {
            'scale_pos_weight': {'type': 'float', 'low': 1.0, 'high': 10.0}
        },
        'LightGBM': {
            'is_unbalance': {'type': 'categorical', 'choices': [True, False]},
            'scale_pos_weight': {'type': 'float', 'low': 1.0, 'high': 10.0}
        },
        'CatBoost': {
            'auto_class_weights': {'type': 'categorical', 'choices': ['Balanced', None]},
            'scale_pos_weight': {'type': 'float', 'low': 1.0, 'high': 10.0}
        },
        'Logistic': {
            'class_weight': {'type': 'categorical', 'choices': ['balanced', None]}
        },
        'SVM': {
            'class_weight': {'type': 'categorical', 'choices': ['balanced', None]}
        },
        'NaiveBayes': {
            'var_smoothing': {'type': 'float', 'low': 1e-10, 'high': 1e-4, 'log': True}
        }
    }
}

# 快速调优配置 - 用于快速实验
QUICK_TUNING_CONFIG = {
    'n_trials': 10,
    'cv_folds': 3
}

# 标准调优配置
STANDARD_TUNING_CONFIG = {
    'n_trials': 50,
    'cv_folds': 5
}

# 深入调优配置 - 用于最终模型
DEEP_TUNING_CONFIG = {
    'n_trials': 100,
    'cv_folds': 10
}

# 获取数据集类型的函数
def get_dataset_type(X):
    """
    根据数据集特征确定其类型，用于选择合适的超参数范围
    
    Args:
        X: 特征数据
        
    Returns:
        str: 数据集类型 ('small', 'large', 'high_dim', 'default')
    """
    n_samples, n_features = X.shape
    
    # 检查是否为小数据集
    if n_samples < 1000:
        return 'small'
    
    # 检查是否为大数据集
    if n_samples > 10000:
        return 'large'
    
    # 检查是否为高维数据集
    if n_features > 100:
        return 'high_dim'
    
    # 默认返回标准配置
    return 'default'

# 获取Optuna超参数空间的函数
def get_optuna_param_space(model_name, dataset_type='default', custom_params=None):
    """
    获取指定模型和数据集类型的Optuna超参数空间
    
    Args:
        model_name: 模型名称
        dataset_type: 数据集类型 ('default', 'small', 'large', 'high_dim', 'imbalanced')
        custom_params: 自定义参数，用于覆盖默认参数
        
    Returns:
        dict: Optuna超参数空间配置
    """
    # 获取默认参数空间
    default_space = OPTUNA_PARAM_SPACE['default'].get(model_name, {})
    
    # 获取指定数据集类型的参数空间
    if dataset_type in OPTUNA_PARAM_SPACE and model_name in OPTUNA_PARAM_SPACE[dataset_type]:
        # 合并默认参数和特定数据集类型的参数
        param_space = default_space.copy()
        param_space.update(OPTUNA_PARAM_SPACE[dataset_type][model_name])
    else:
        param_space = default_space
    
    # 如果提供了自定义参数，则覆盖默认参数
    if custom_params:
        param_space.update(custom_params)
    
    return param_space

# 配置中文字体
def configure_chinese_font():
    """
    根据操作系统配置支持中文显示的字体
    """
    # 检测操作系统类型
    system = platform.system()
    
    if system == 'Windows':
        # Windows系统的中文字体选项
        font_list = ['Microsoft YaHei', 'SimHei', 'SimSun', 'FangSong']
    elif system == 'Darwin':  # macOS
        # macOS系统的中文字体选项
        font_list = ['PingFang SC', 'STHeiti', 'Heiti TC', 'Hiragino Sans GB']
    else:  # Linux和其他系统
        # Linux系统的中文字体选项
        font_list = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Droid Sans Fallback']
    
    # 查找系统中可用的字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 找到第一个可用的中文字体
    chinese_font = None
    for font in font_list:
        if font in available_fonts:
            chinese_font = font
            break
    
    # 如果找不到中文字体，尝试添加自定义字体
    if chinese_font is None:
        # 在项目中添加一个字体文件夹
        font_dir = PROJECT_ROOT / 'fonts'
        font_dir.mkdir(exist_ok=True)
        
        # 检查是否有自带的中文字体文件
        custom_font_path = font_dir / 'simhei.ttf'  # 可以下载并放置SimHei字体到此处
        if custom_font_path.exists():
            # 添加自定义字体
            fm.fontManager.addfont(str(custom_font_path))
            chinese_font = 'SimHei'
        else:
            # 如果没有找到任何中文字体，使用默认字体并记录警告
            import logging
            logging.warning("找不到支持中文显示的字体，图表中的中文可能无法正确显示")
            chinese_font = fm.rcParams['font.sans-serif'][0]
    
    return chinese_font

# 获取中文字体
CHINESE_FONT = configure_chinese_font()

# 绘图配置
PLOT_CONFIG = {
    'dpi': 150,
    'figsize': (10, 8),
    'save_format': 'png',
    'font_family': ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
}

# 集成学习配置
ENSEMBLE_CONFIG = {
    'voting_methods': ['hard', 'soft'],
    'stacking_cv': 5,
    'weighted_methods': ['performance', 'diversity', 'equal'],
    'dynamic_selection_methods': ['confidence', 'local_accuracy'],
    'ensemble_methods': ['voting', 'bagging', 'boosting', 'stacking'],
    'default_ensemble_methods': ['voting', 'stacking'],
    'n_estimators': 10,
    'enable_shap': True,
    'save_results': True
}

# 多数据源集成配置
MULTI_DATA_CONFIG = {
    'ensemble_methods': ['voting', 'stacking', 'weighted'],
    'ensemble_data_strategies': ['unified', 'original', 'combined']
}

# 模型训练函数映射（字符串形式，用于动态导入）
MODEL_FUNCTION_NAMES = {
    'DecisionTree': 'train_decision_tree',
    'RandomForest': 'train_random_forest',
    'XGBoost': 'train_xgboost',
    'LightGBM': 'train_lightgbm',
    'CatBoost': 'train_catboost',
    'Logistic': 'train_logistic',
    'SVM': 'train_svm',
    'KNN': 'train_knn',
    'NaiveBayes': 'train_naive_bayes',
    'NeuralNet': 'train_neural_net'
}

# 获取模型训练函数的实际引用（延迟导入避免循环依赖）
def get_model_functions():
    """
    获取模型训练函数的实际引用
    使用延迟导入避免循环依赖问题
    """
    try:
        from model_training import (
            train_decision_tree, train_random_forest, train_xgboost,
            train_lightgbm, train_catboost, train_logistic,
            train_svm, train_knn, train_naive_bayes, train_neural_net
        )

        return {
            'DecisionTree': train_decision_tree,
            'RandomForest': train_random_forest,
            'XGBoost': train_xgboost,
            'LightGBM': train_lightgbm,
            'CatBoost': train_catboost,
            'Logistic': train_logistic,
            'SVM': train_svm,
            'KNN': train_knn,
            'NaiveBayes': train_naive_bayes,
            'NeuralNet': train_neural_net
        }
    except ImportError as e:
        print(f"警告：无法导入模型训练函数: {e}")
        return {}

# 模型名称和显示名称映射
MODEL_DISPLAY_NAMES = {
    'DecisionTree': 'Decision Tree',
    'RandomForest': 'Random Forest',
    'XGBoost': 'XGBoost',
    'LightGBM': 'LightGBM',
    'CatBoost': 'CatBoost',
    'Logistic': 'Logistic Regression',
    'SVM': 'SVM',
    'KNN': 'KNN',
    'NaiveBayes': 'Naive Bayes',
    'NeuralNet': 'Neural Network'
}

# 所有支持的模型列表
MODEL_NAMES = list(MODEL_DISPLAY_NAMES.keys())

# 指标配置
METRICS_CONFIG = {
    'classification': ['accuracy', 'precision', 'recall', 'f1', 'roc_auc', 'average_precision'],
    'primary_metric': 'roc_auc'
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S'
}

# 特征选择配置
FEATURE_SELECTION_CONFIG = {
    'tree_models': ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost'],
    'linear_models': ['Logistic', 'SVM'],
    'instance_based_models': ['KNN', 'NaiveBayes'],
    'neural_models': ['NeuralNet'],
    
    'tree_methods': 'importance',
    'linear_methods': 'rfecv',
    'instance_based_methods': 'mutual_info',
    'neural_methods': 'kbest',
    'default_method': 'rfecv'
}

# GPU 配置
GPU_CONFIG = {
    'use_gpu': True,  # 开启GPU加速
    'gpu_ids': [0],   # 要使用的GPU ID列表
    'precision': 'float32',  # 精度: 'float16', 'float32'或'float64'
    'auto_detect': True,  # 自动检测GPU可用性
    'fallback_to_cpu': True  # GPU不可用时自动回退到CPU
}

def detect_gpu_availability():
    """
    检测GPU和CUDA可用性

    Returns:
        dict: GPU检测结果
    """
    gpu_info = {
        'cuda_available': False,
        'gpu_count': 0,
        'gpu_names': [],
        'xgboost_gpu': False,
        'lightgbm_gpu': False,
        'catboost_gpu': False,
        'cuda_version': None
    }

    try:
        # 检查NVIDIA-SMI
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            gpu_info['nvidia_smi_available'] = True

        # 检查XGBoost GPU支持
        try:
            import xgboost as xgb
            # 尝试创建GPU配置的XGBoost模型
            test_model = xgb.XGBClassifier(device='cuda', tree_method='hist', n_estimators=1)
            gpu_info['xgboost_gpu'] = True
        except:
            gpu_info['xgboost_gpu'] = False

        # 检查LightGBM GPU支持
        try:
            import lightgbm as lgb
            # 检查是否编译了GPU支持
            gpu_info['lightgbm_gpu'] = lgb.LGBMClassifier().get_params().get('device', 'cpu') != 'cpu'
        except:
            gpu_info['lightgbm_gpu'] = False

        # 检查CatBoost GPU支持
        try:
            import catboost as cb
            # CatBoost通常支持GPU
            gpu_info['catboost_gpu'] = True
        except:
            gpu_info['catboost_gpu'] = False

    except Exception as e:
        print(f"GPU检测过程中出现错误: {e}")

    return gpu_info

def get_optimized_gpu_config():
    """
    获取优化的GPU配置

    Returns:
        dict: 优化后的GPU配置
    """
    gpu_info = detect_gpu_availability()

    # 如果启用了自动检测
    if GPU_CONFIG.get('auto_detect', True):
        # 根据检测结果调整配置
        optimized_config = GPU_CONFIG.copy()

        # 如果没有可用的GPU支持，自动禁用GPU
        if not any([gpu_info['xgboost_gpu'], gpu_info['lightgbm_gpu'], gpu_info['catboost_gpu']]):
            if GPU_CONFIG.get('fallback_to_cpu', True):
                optimized_config['use_gpu'] = False
                print("⚠️  未检测到GPU支持，自动切换到CPU模式")

        return optimized_config

    return GPU_CONFIG

def get_absolute_path(relative_path):
    """
    将相对路径转换为绝对路径
    """
    if isinstance(relative_path, str):
        return PROJECT_ROOT / relative_path
    return relative_path

def get_model_hyperparameters(model_name):
    """
    获取指定模型的超参数网格
    """
    return HYPERPARAMETER_GRIDS.get(model_name, {})

def get_feature_selection_config(model_name):
    """
    获取模型对应的特征选择配置
    """
    if model_name in FEATURE_SELECTION_CONFIG['tree_models']:
        return 'tree', FEATURE_SELECTION_CONFIG['tree_methods']
    elif model_name in FEATURE_SELECTION_CONFIG['linear_models']:
        return 'linear', FEATURE_SELECTION_CONFIG['linear_methods']
    elif model_name in FEATURE_SELECTION_CONFIG['instance_based_models']:
        return 'all', FEATURE_SELECTION_CONFIG['instance_based_methods']
    elif model_name in FEATURE_SELECTION_CONFIG['neural_models']:
        return 'all', FEATURE_SELECTION_CONFIG['neural_methods']
    else:
        return 'all', FEATURE_SELECTION_CONFIG['default_method']

def initialize_matplotlib():
    """
    初始化matplotlib设置，使用英文字体
    """
    import matplotlib as mpl
    import matplotlib.pyplot as plt
    
    # 设置全局字体
    mpl.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
    mpl.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 设置DPI和图表大小
    mpl.rcParams['figure.dpi'] = PLOT_CONFIG['dpi']
    mpl.rcParams['figure.figsize'] = PLOT_CONFIG['figsize']
    
    # 设置风格
    plt.style.use('seaborn-v0_8-whitegrid')
    
    return plt 
