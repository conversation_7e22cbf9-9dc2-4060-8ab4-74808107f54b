#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的绘图功能
"""

import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.datasets import make_classification
import matplotlib.pyplot as plt
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

try:
    from plot_single_model import plot_roc_curve, plot_learning_curve, CONFIG
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖都已正确安装")
    sys.exit(1)

def create_test_data():
    """创建测试数据"""
    # 生成平衡的二分类数据
    X, y = make_classification(
        n_samples=200,
        n_features=10,
        n_informative=5,
        n_redundant=2,
        n_clusters_per_class=1,
        weights=[0.5, 0.5],  # 平衡数据
        random_state=42
    )
    
    return train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)

def test_improved_plots():
    """测试改进后的绘图功能"""
    print("创建测试数据...")
    X_train, X_test, y_train, y_test = create_test_data()
    
    print("训练RandomForest模型...")
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=5,
        random_state=42
    )
    model.fit(X_train, y_train)
    
    # 设置模型名称
    CONFIG['model_name'] = 'RandomForest'
    
    print("绘制改进后的ROC曲线...")
    try:
        fig_roc, auc_score = plot_roc_curve(model, X_test, y_test, 'improved_roc_curve.pdf')
        if fig_roc is not None:
            print(f"✓ ROC曲线绘制成功，AUC = {auc_score:.3f}")
            plt.show()
        else:
            print("✗ ROC曲线绘制失败")
    except Exception as e:
        print(f"✗ ROC曲线绘制出错: {e}")
    
    print("绘制改进后的学习曲线...")
    try:
        fig_learning, ax_learning = plot_learning_curve(model, X_train, y_train, cv=3, filename='improved_learning_curve.pdf')
        if fig_learning is not None:
            print("✓ 学习曲线绘制成功")
            plt.show()
        else:
            print("✗ 学习曲线绘制失败")
    except Exception as e:
        print(f"✗ 学习曲线绘制出错: {e}")
    
    print("\n测试完成！")
    print("改进内容:")
    print("1. 提高了图形分辨率 (DPI: 300)")
    print("2. 增大了字体大小")
    print("3. 改善了颜色对比度")
    print("4. 优化了图例样式")
    print("5. 改进了注释框设计")
    print("6. 美化了坐标轴样式")

if __name__ == "__main__":
    test_improved_plots()
