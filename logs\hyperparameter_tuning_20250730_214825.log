2025-07-30 21:48:25 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 144, 'max_depth': 9, 'min_samples_split': 7, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-07-30 21:48:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_214854.html
2025-07-30 21:48:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_214854.html
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 29.03 秒
2025-07-30 21:49:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:49:22 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/学习历史曲线.pdf
2025-07-30 21:49:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:49:43 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/参数重要性.pdf
2025-07-30 21:50:03 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:50:04 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/参数关系.pdf
2025-07-30 21:50:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:50:30 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/1参数关系.pdf
