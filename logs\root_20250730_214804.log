2025-07-30 21:48:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-30 21:48:06 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\multi_model_01_updated\output\data_exploration
2025-07-30 21:48:06 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-07-30 21:48:06 - GUI - INFO - GUI界面初始化完成
2025-07-30 21:48:25 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-07-30 21:48:25 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-07-30 21:48:25 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 144, 'max_depth': 9, 'min_samples_split': 7, 'min_samples_leaf': 9, 'max_features': 'log2'}
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-07-30 21:48:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:48:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_214854.html
2025-07-30 21:48:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:48:54 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_214854.html
2025-07-30 21:48:54 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 29.03 秒
2025-07-30 21:49:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:49:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:49:22 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/学习历史曲线.pdf
2025-07-30 21:49:42 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:49:42 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:49:43 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/参数重要性.pdf
2025-07-30 21:50:03 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:50:03 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:50:04 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/参数关系.pdf
2025-07-30 21:50:30 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-07-30 21:50:30 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 21:50:30 - hyperparameter_tuning - INFO - 使用matplotlib保存图表到: D:/Code/multi_model_01_updated/output/rf/1参数关系.pdf
