2025-07-30 22:08:54 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 199, 'max_depth': 7, 'min_samples_split': 11, 'min_samples_leaf': 3, 'max_features': 'log2'}
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8411
2025-07-30 22:09:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_220928.html
2025-07-30 22:09:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_220928.html
2025-07-30 22:09:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 34.69 秒
