2025-07-30 22:43:37 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 10，数据集类型: small
2025-07-30 22:43:39 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 126, 'max_depth': 10, 'min_samples_split': 9, 'min_samples_leaf': 5, 'max_features': 'sqrt'}
2025-07-30 22:43:39 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8041
2025-07-30 22:43:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:43:39 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_224339.html
2025-07-30 22:43:39 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:43:39 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_224339.html
2025-07-30 22:43:39 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.84 秒
2025-07-30 22:43:52 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 10，数据集类型: balanced
2025-07-30 22:43:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 293, 'max_depth': 14, 'min_samples_split': 2, 'min_samples_leaf': 3, 'max_features': 'log2'}
2025-07-30 22:43:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.8124
2025-07-30 22:43:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:43:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_224355.html
2025-07-30 22:43:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-07-30 22:43:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_224355.html
2025-07-30 22:43:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.42 秒
