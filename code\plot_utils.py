#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘图工具模块
提供通用的绘图和图像保存功能
"""

import os
import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import time
import logging
import pandas as pd

# 设置日志
logger = logging.getLogger("plot_utils")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)

# 尝试导入配置模块
try:
    from config import OUTPUT_PATH, PLOT_CONFIG, MODEL_DISPLAY_NAMES
except ImportError:
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    PLOT_CONFIG = {'dpi': 150, 'figsize': (10, 8), 'save_format': 'png'}
    MODEL_DISPLAY_NAMES = {
        'DecisionTree': 'Decision Tree',
        'RandomForest': 'Random Forest',
        'XGBoost': 'XGBoost',
        'LightGBM': 'LightGBM',
        'CatBoost': 'CatBoost',
        'Logistic': 'Logistic Regression',
        'SVM': 'SVM',
        'KNN': 'KNN',
        'NaiveBayes': 'Naive Bayes',
        'NeuralNet': 'Neural Network'
    }

# 配置更现代化的绘图样式
plt.style.use('seaborn-v0_8-whitegrid')

# 设置默认字体大小和风格
PLOT_CONFIG = {
    'figsize': (10, 8),  # 默认图形大小
    'dpi': 150,          # 默认分辨率
    'fontsize': {
        'title': 16,      # 标题字体大小
        'axes_title': 14, # 子图标题字体大小
        'label': 12,      # 轴标签字体大小
        'tick': 10,       # 刻度标签字体大小
        'legend': 10,     # 图例字体大小
        'annotation': 10  # 注释字体大小
    },
    'colors': {
        'train': '#1f77b4',    # 训练集颜色（蓝色）
        'valid': '#ff7f0e',    # 验证集颜色（橙色）
        'test': '#2ca02c',     # 测试集颜色（绿色）
        'highlight': '#d62728', # 高亮颜色（红色）
        'grid': '#cccccc'      # 网格线颜色（浅灰色）
    },
    'marker_size': 8,    # 默认标记大小
    'line_width': 2,     # 默认线宽
    'grid_alpha': 0.3    # 网格透明度
}

# 设置字体，使用更安全的方式
mpl.rcParams['font.family'] = 'sans-serif'
mpl.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
mpl.rcParams['axes.unicode_minus'] = False

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("plot_utils")
except ImportError:
    # 我们已经在文件开头设置了基本的logger，这里不需要重复设置
    pass

# 使用英文代替中文术语的映射
TERM_MAPPING = {
    '假阳性率 (FPR)': 'False Positive Rate (FPR)',
    '真阳性率 (TPR)': 'True Positive Rate (TPR)',
    'ROC曲线': 'ROC Curve',
    '混淆矩阵': 'Confusion Matrix',
    '预测类别': 'Predicted Class',
    '真实类别': 'True Class',
    '特征重要性': 'Feature Importance',
    '重要性': 'Importance',
    '特征': 'Feature',
    '学习曲线': 'Learning Curve',
    '训练集得分': 'Training Score',
    '测试集得分': 'Test Score',
    '训练样本数': 'Training Samples',
    '得分': 'Score',
    '超参数调优': 'Hyperparameter Tuning',
    '交叉验证得分': 'Cross-validation Score',
    '最佳值': 'Best Value',
    '模型性能比较': 'Model Performance Comparison',
    '模型': 'Model',
    '训练历史': 'Training History',
    '训练损失': 'Training Loss',
    '验证损失': 'Validation Loss',
    '轮次': 'Epochs',
    '损失': 'Loss',
    '归一化': 'Normalized'
}

def setup_matplotlib_for_chinese():
    """
    配置matplotlib以支持中文显示
    """
    import platform
    import matplotlib as mpl
    # 添加更多中文字体支持，特别是微软雅黑和宋体（Windows系统常用）
    mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
    # 修复负号显示问题
    mpl.rcParams['axes.unicode_minus'] = False
    # 设置绘图风格
    plt.style.use('seaborn-v0_8-whitegrid')

    # 确保matplotlib中文字体可用
    if platform.system() == 'Windows':
        # Windows系统中文字体处理
        from matplotlib.font_manager import FontProperties
        try:
            font = FontProperties(fname=r"C:\\Windows\\Fonts\\msyh.ttc", size=10) # 微软雅黑
            mpl.rcParams['font.family'] = font.get_name()
        except:
            try:
                font = FontProperties(fname=r"C:\\Windows\\Fonts\\simhei.ttf", size=10) # 黑体
                mpl.rcParams['font.family'] = font.get_name()
            except:
                # 如果以上字体都不可用，尝试使用已安装的中文字体
                from matplotlib.font_manager import findfont, FontProperties
                try:
                    findfont(FontProperties(family=['Microsoft YaHei', 'SimHei', 'SimSun']))
                    mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun'] + mpl.rcParams['font.sans-serif']
                except:
                    # 使用 get_default_logger 记录日志
                    try:
                        from logger import get_default_logger
                        logger = get_default_logger("plot_utils_font_setup")
                        logger.warning("无法找到合适的中文字体，图表可能无法正确显示中文")
                    except ImportError:
                        print("警告: 无法找到合适的中文字体，图表可能无法正确显示中文")
    
# 执行字体设置
setup_matplotlib_for_chinese()

def get_save_path(model_name, plot_type, file_name=None, create_dirs=True):
    """
    根据模型名称和绘图类型生成保存路径
    
    Args:
        model_name: 模型名称，如果为None或'All'则使用common目录
        plot_type: 绘图类型(如'roc', 'confusion', 'feature_importance'等)
        file_name: 文件名，如果为None则自动生成
        create_dirs: 是否自动创建目录
        
    Returns:
        Path: 保存路径
    """
    # 如果模型名称为None或'all'，使用combined目录
    if model_name is None or model_name.lower() == 'all':
        model_dir = OUTPUT_PATH / 'combined'
    else:
        # 为每个模型创建单独的目录
        model_dir = OUTPUT_PATH / model_name
    
    # 为每种绘图类型创建子目录
    plot_dir = model_dir / plot_type if plot_type else model_dir
    
    # 创建目录
    if create_dirs:
        plot_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成文件名
    if file_name is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        if model_name.lower() == 'all':
            prefix = 'combined'
        else:
            prefix = model_name
        file_name = f"{prefix}_{plot_type}_{timestamp}.{PLOT_CONFIG.get('save_format', 'png')}"
    elif not file_name.endswith(f".{PLOT_CONFIG.get('save_format', 'png')}"):
        file_name = f"{file_name}.{PLOT_CONFIG.get('save_format', 'png')}"
    
    return plot_dir / file_name

def translate_term(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

def optimize_figure_for_pdf(fig, fontsize_config=None):
    """
    优化图形以获得更好的PDF输出效果

    Args:
        fig: matplotlib图形对象
        fontsize_config: 字体大小配置字典
    """
    if fontsize_config is None:
        fontsize_config = PLOT_CONFIG.get('fontsize', {})

    # 调整整体布局
    fig.tight_layout(pad=2.0)

    # 优化每个子图
    for ax in fig.get_axes():
        # 调整标题
        if ax.get_title():
            title_size = fontsize_config.get('title', 14)
            ax.set_title(ax.get_title(), fontsize=title_size, fontweight='bold')

        # 调整轴标签
        label_size = fontsize_config.get('label', 12)
        if ax.get_xlabel():
            ax.set_xlabel(ax.get_xlabel(), fontsize=label_size)
        if ax.get_ylabel():
            ax.set_ylabel(ax.get_ylabel(), fontsize=label_size)

        # 调整刻度标签
        tick_size = fontsize_config.get('tick', 10)
        ax.tick_params(axis='both', which='major', labelsize=tick_size)

        # 调整图例
        legend = ax.get_legend()
        if legend:
            legend_size = fontsize_config.get('legend', 10)
            legend.set_fontsize(legend_size)

        # 确保网格线清晰可见
        if ax.get_grid():
            ax.grid(True, alpha=0.3, linewidth=0.5)

class PlotManager:
    """
    统一的绘图管理器，消除重复的绘图代码
    """

    def __init__(self, output_path=None, dpi=None, figsize=None):
        """
        初始化绘图管理器

        Args:
            output_path: 输出路径
            dpi: 图像分辨率
            figsize: 图像大小
        """
        self.output_path = output_path or OUTPUT_PATH
        self.dpi = dpi or PLOT_CONFIG.get('dpi', 300)
        self.figsize = figsize or PLOT_CONFIG.get('figsize', (12, 9))
        self.save_format = PLOT_CONFIG.get('save_format', 'pdf')
        self.fontsize = PLOT_CONFIG.get('fontsize', {
            'title': 14, 'label': 12, 'tick': 10, 'legend': 10
        })
        self.pdf_params = PLOT_CONFIG.get('pdf_params', {
            'bbox_inches': 'tight', 'facecolor': 'white',
            'edgecolor': 'none', 'pad_inches': 0.3, 'transparent': False
        })

    def create_figure(self, figsize=None):
        """创建标准化的图形"""
        figsize = figsize or self.figsize
        fig, ax = plt.subplots(figsize=figsize)

        # 设置中文字体
        if FONT_PROPERTIES:
            ax.tick_params(labelsize=10)

        return fig, ax

    def save_plot(self, fig, model_name, plot_type, file_name=None, close_fig=True):
        """
        保存图形到指定目录

        Args:
            fig: matplotlib图形对象
            model_name: 模型名称
            plot_type: 绘图类型
            file_name: 文件名，如果为None则自动生成
            close_fig: 保存后是否关闭图形

        Returns:
            str: 保存的文件路径
        """
        save_path = get_save_path(model_name, plot_type, file_name)

        try:
            # 确保目标目录存在
            save_dir = os.path.dirname(save_path)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                
            # 检查是否为PDF文件
            is_pdf = str(save_path).lower().endswith('.pdf')
            
            if is_pdf:
                try:
                    # 优化PDF保存设置
                    # 使用配置文件中的PDF参数
                    pdf_params = {
                        'dpi': self.dpi,
                        'format': 'pdf',
                        **self.pdf_params  # 使用配置文件中的PDF参数
                    }

                    # 使用专门的PDF优化函数
                    optimize_figure_for_pdf(fig, self.fontsize)

                    # 保存为PDF
                    fig.savefig(save_path, **pdf_params)
                    logger.info(f"图形已保存为PDF: {save_path}")
                except Exception as pdf_error:
                    logger.warning(f"保存为PDF失败: {pdf_error}，尝试备选方案")
                    
                    try:
                        # 创建新的图形并复制内容
                        new_fig = plt.figure(figsize=fig.get_size_inches())
                        for i, ax_old in enumerate(fig.get_axes()):
                            # 复制轴到新图形
                            ax_new = new_fig.add_subplot(len(fig.get_axes()), 1, i+1)
                            
                            # 复制线条
                            for line in ax_old.get_lines():
                                ax_new.plot(line.get_xdata(), line.get_ydata(), 
                                          color=line.get_color(), 
                                          linestyle=line.get_linestyle(),
                                          marker=line.get_marker(),
                                          label=line.get_label())
                            
                            # 复制标题和标签
                            ax_new.set_title(ax_old.get_title())
                            ax_new.set_xlabel(ax_old.get_xlabel())
                            ax_new.set_ylabel(ax_old.get_ylabel())
                            
                            # 复制图例
                            if ax_old.get_legend():
                                ax_new.legend()
                                
                            # 复制网格设置
                            ax_new.grid(ax_old.get_grid())
                        
                        # 调整布局
                        new_fig.tight_layout()
                        
                        # 保存新图形
                        new_fig.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
                        plt.close(new_fig)
                        logger.info(f"使用备选方案保存图形成功: {save_path}")
                    except Exception as alt_error:
                        # 如果备选方案也失败，尝试保存为PNG
                        logger.warning(f"备选方案失败: {alt_error}，尝试保存为PNG")
                        png_path = str(save_path).replace('.pdf', '.png')
                        fig.savefig(png_path, dpi=self.dpi, bbox_inches='tight', format='png')
                        logger.info(f"图形已保存为PNG: {png_path}")
                        save_path = png_path
            else:
                # 非PDF格式直接保存
                fig.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
                logger.info(f"图形已保存到: {save_path}")

            if close_fig:
                plt.close(fig)

            return str(save_path)
        except Exception as e:
            logger.error(f"保存图形失败: {e}")
            if close_fig:
                plt.close(fig)
            return None

    def apply_style(self, ax, title=None, xlabel=None, ylabel=None):
        """应用统一的样式"""
        if title:
            ax.set_title(title, fontsize=PLOT_CONFIG['fontsize']['title'], fontweight='bold', fontproperties=FONT_PROPERTIES)
        if xlabel:
            ax.set_xlabel(xlabel, fontsize=PLOT_CONFIG['fontsize']['label'], fontproperties=FONT_PROPERTIES)
        if ylabel:
            ax.set_ylabel(ylabel, fontsize=PLOT_CONFIG['fontsize']['label'], fontproperties=FONT_PROPERTIES)

        # 设置网格
        ax.grid(True, alpha=PLOT_CONFIG['grid_alpha'], linestyle='--', color=PLOT_CONFIG['colors']['grid'])
        
        # 设置边框
        for spine in ['top', 'right']:
            ax.spines[spine].set_visible(False)
        
        # 设置刻度标签字体
        ax.tick_params(axis='both', labelsize=PLOT_CONFIG['fontsize']['tick'])
        
        if FONT_PROPERTIES:
            for label in ax.get_xticklabels() + ax.get_yticklabels():
                label.set_fontproperties(FONT_PROPERTIES)


# 全局绘图管理器实例（延迟初始化）
plot_manager = None

def get_plot_manager():
    """获取全局绘图管理器实例（延迟初始化）"""
    global plot_manager
    if plot_manager is None:
        plot_manager = PlotManager()
    return plot_manager

def save_plot(fig, model_name, plot_type, file_name=None, close_fig=True):
    """
    保存图形到指定目录（兼容旧版本）

    Args:
        fig: matplotlib图形对象
        model_name: 模型名称
        plot_type: 绘图类型
        file_name: 文件名，如果为None则自动生成
        close_fig: 保存后是否关闭图形

    Returns:
        str: 保存的文件路径
    """
    return get_plot_manager().save_plot(fig, model_name, plot_type, file_name, close_fig)

def create_figure(figsize=None):
    """
    创建新的图形对象
    
    Args:
        figsize: 图形大小，如果为None则使用配置中的默认值
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    if figsize is None:
        figsize = PLOT_CONFIG.get('figsize', (10, 8))
    
    fig, ax = plt.subplots(figsize=figsize)
    return fig, ax

def get_font_properties(font_size=10):
    """
    获取适合中文显示的字体属性
    
    Args:
        font_size: 字体大小
    
    Returns:
        FontProperties: 字体属性对象，如果找不到合适的中文字体则返回None
    """
    import platform
    try:
        from matplotlib.font_manager import FontProperties
        if platform.system() == 'Windows':
            font_paths = [
                r"C:\Windows\Fonts\msyh.ttc",  # 微软雅黑
                r"C:\Windows\Fonts\simhei.ttf", # 黑体
                r"C:\Windows\Fonts\simsun.ttc"  # 宋体
            ]
            for path in font_paths:
                if os.path.exists(path):
                    return FontProperties(fname=path, size=font_size)
            # 如果常用字体不存在，则按名称搜索
            return FontProperties(family=['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif'], size=font_size)
        else:
            # 对于非Windows系统
            return FontProperties(family=['SimHei', 'DejaVu Sans', 'sans-serif'], size=font_size)
    except Exception as e:
        logger.warning(f"获取字体属性失败: {e}")
        return None

# 获取全局字体属性
FONT_PROPERTIES = get_font_properties()

def plot_roc_curve(fpr, tpr, auc_value, model_name, ax=None):
    """绘制ROC曲线"""
    if ax is None:
        fig, ax = plt.subplots(figsize=PLOT_CONFIG['figsize'])
    
    # 使用更好看的颜色和更粗的线
    ax.plot(fpr, tpr, color=PLOT_CONFIG['colors']['test'], 
            linewidth=PLOT_CONFIG['line_width'], 
            label=f"{model_name} (AUC = {auc_value:.3f})")
    
    # 添加对角线参考
    ax.plot([0, 1], [0, 1], color='gray', linestyle='--', linewidth=1.5, alpha=0.7, label="Random")
    
    # 找到约登指数最大点（最优阈值点）
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = optimal_idx / len(tpr) if len(tpr) > 0 else 0
    
    # 标记最优点
    ax.scatter(fpr[optimal_idx], tpr[optimal_idx], 
               s=100, color=PLOT_CONFIG['colors']['highlight'], alpha=0.8, 
               label=f"Optimal (t={optimal_threshold:.3f})")
    
    # 设置轴范围和标签
    ax.set_xlim([0.0, 1.0])
    ax.set_ylim([0.0, 1.05])
    
    # 应用样式
    title = f"ROC Curve - {model_name}"
    subtitle = f"(Balanced Data, n={len(fpr)})"
    ax.set_title(f"{title}\n{subtitle}", fontsize=PLOT_CONFIG['fontsize']['title'])
    ax.set_xlabel("False Positive Rate (1 - Specificity)", fontsize=PLOT_CONFIG['fontsize']['label'])
    ax.set_ylabel("True Positive Rate (Sensitivity)", fontsize=PLOT_CONFIG['fontsize']['label'])
    
    # 设置网格
    ax.grid(True, alpha=PLOT_CONFIG['grid_alpha'], linestyle='--', color=PLOT_CONFIG['colors']['grid'])
    
    # 去除顶部和右侧边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # 添加图例
    ax.legend(loc="lower right", fontsize=PLOT_CONFIG['fontsize']['legend'])
    
    return fig if ax is None else None, ax

def plot_confusion_matrix(cm, model_name, class_names=None, normalize=False, ax=None):
    """
    绘制混淆矩阵
    
    Args:
        cm: 混淆矩阵
        model_name: 模型名称
        class_names: 类别名称列表
        normalize: 是否归一化
        ax: matplotlib轴对象，如果为None则创建新的图形
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    # 获取模型的显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    
    # 如果没有提供轴对象，创建新的图形
    if ax is None:
        fig, ax = create_figure()
        created_fig = True
    else:
        fig = ax.figure
        created_fig = False
    
    # 归一化
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        fmt = '.2f'
        title_suffix = f"({translate_term('归一化')})"
    else:
        fmt = 'd'
        title_suffix = ''
    
    # 如果没有提供类别名称，使用默认值
    if class_names is None:
        class_names = ['0', '1']
    
    # 使用seaborn绘制热图
    sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names, ax=ax)
    
    # 设置标签和标题
    ax.set_xlabel(translate_term('预测类别'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate_term('真实类别'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_title(f"{translate_term('混淆矩阵')} - {display_name} {title_suffix}", fontsize=14, fontproperties=FONT_PROPERTIES)
    
    # 调整布局
    fig.tight_layout()
    
    # 如果是新创建的图形，保存并返回
    if created_fig:
        save_plot(fig, model_name, 'confusion_matrix')
    
    return fig, ax

def plot_feature_importance(importance, feature_names, model_name, top_n=20, ax=None):
    """
    绘制特征重要性
    
    Args:
        importance: 特征重要性数组
        feature_names: 特征名称列表
        model_name: 模型名称
        top_n: 显示前N个重要特征
        ax: matplotlib轴对象，如果为None则创建新的图形
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    # 获取模型的显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    
    # 如果没有提供轴对象，创建新的图形
    if ax is None:
        fig, ax = create_figure(figsize=(12, 8))
        created_fig = True
    else:
        fig = ax.figure
        created_fig = False
    
    # 确保特征名称和重要性长度一致
    if len(feature_names) != len(importance):
        logger.warning(f"特征名称长度({len(feature_names)})与重要性长度({len(importance)})不一致")
        # 使用索引作为特征名称
        feature_names = [f"Feature {i}" for i in range(len(importance))]
    
    # 将特征重要性和名称组合并排序
    features = list(zip(feature_names, importance))
    features.sort(key=lambda x: x[1], reverse=True)
    
    # 选择前N个特征
    if top_n is not None and top_n < len(features):
        features = features[:top_n]
    
    # 提取排序后的特征名称和重要性
    names, values = zip(*features)
    
    # 绘制水平条形图
    ax.barh(range(len(names)), values, align='center')
    ax.set_yticks(range(len(names)))
    ax.set_yticklabels(names, fontproperties=FONT_PROPERTIES)
    
    # 设置标签和标题
    ax.set_xlabel(translate_term('重要性'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate_term('特征'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_title(f"{translate_term('特征重要性')} - {display_name}", fontsize=14, fontproperties=FONT_PROPERTIES)
    
    # 调整布局
    fig.tight_layout()
    
    # 如果是新创建的图形，保存并返回
    if created_fig:
        save_plot(fig, model_name, 'feature_importance')
    
    return fig, ax

def plot_learning_curve(train_sizes, train_scores, test_scores, model_name, ax=None):
    """绘制学习曲线"""
    if ax is None:
        fig, ax = plt.subplots(figsize=PLOT_CONFIG['figsize'])
    
    # 计算平均值和标准差
    train_mean = np.mean(train_scores, axis=1)
    train_std = np.std(train_scores, axis=1)
    test_mean = np.mean(test_scores, axis=1)
    test_std = np.std(test_scores, axis=1)
    
    # 填充标准差区域（使用半透明填充）
    ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                   alpha=0.1, color=PLOT_CONFIG['colors']['train'])
    ax.fill_between(train_sizes, test_mean - test_std, test_mean + test_std, 
                   alpha=0.1, color=PLOT_CONFIG['colors']['valid'])
    
    # 绘制平均值线
    ax.plot(train_sizes, train_mean, 'o-', color=PLOT_CONFIG['colors']['train'], 
           markersize=PLOT_CONFIG['marker_size'], linewidth=PLOT_CONFIG['line_width'], label="Training Set")
    ax.plot(train_sizes, test_mean, 'o-', color=PLOT_CONFIG['colors']['valid'], 
           markersize=PLOT_CONFIG['marker_size'], linewidth=PLOT_CONFIG['line_width'], label="Validation Set")
    
    # 设置轴范围
    ax.set_ylim([0.8, 1.01])  # 调整Y轴范围以便更好地展示差异
    
    # 标记训练集和验证集的最终性能
    final_train = train_mean[-1]
    final_test = test_mean[-1]
    gap = final_train - final_test
    
    # 添加性能注释框
    perf_text = f"Training AUC: {final_train:.3f}\nValidation AUC: {final_test:.3f}\nGap: {gap:.3f}"
    
    # 根据差距评估模型
    if gap < 0.05:
        evaluation = "Good model performance"
        box_color = "lightgreen"
    elif gap < 0.1:
        evaluation = "Slight overfitting"
        box_color = "khaki"
    else:
        evaluation = "Significant overfitting"
        box_color = "lightcoral"
    
    # 添加评估注释
    bbox_props = dict(boxstyle="round,pad=0.5", facecolor=box_color, alpha=0.7)
    ax.text(0.05, 0.95, perf_text + "\n\n" + evaluation, transform=ax.transAxes, 
           fontsize=PLOT_CONFIG['fontsize']['annotation'], verticalalignment='top', bbox=bbox_props)
    
    # 应用样式
    ax.set_title(f"Learning Curve - {model_name}", fontsize=PLOT_CONFIG['fontsize']['title'])
    ax.set_xlabel("Training Examples", fontsize=PLOT_CONFIG['fontsize']['label'])
    ax.set_ylabel("AUC Score", fontsize=PLOT_CONFIG['fontsize']['label'])
    
    # 设置网格
    ax.grid(True, alpha=PLOT_CONFIG['grid_alpha'], linestyle='--', color=PLOT_CONFIG['colors']['grid'])
    
    # 去除顶部和右侧边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # 添加图例
    ax.legend(loc="lower right", fontsize=PLOT_CONFIG['fontsize']['legend'])
    
    return fig if ax is None else None, ax

def plot_hyperparameter_search(param_name, param_values, cv_results, model_name, ax=None):
    """
    绘制超参数调优结果
    
    Args:
        param_name: 超参数名称
        param_values: 超参数值列表
        cv_results: 交叉验证结果
        model_name: 模型名称
        ax: matplotlib轴对象，如果为None则创建新的图形
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    # 获取模型的显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    
    # 如果没有提供轴对象，创建新的图形
    if ax is None:
        fig, ax = create_figure()
        created_fig = True
    else:
        fig = ax.figure
        created_fig = False
    
    # 绘制超参数调优结果
    ax.plot(param_values, cv_results, 'o-')
    
    # 设置标签和标题
    ax.set_xlabel(param_name, fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate_term('交叉验证得分'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_title(f"{translate_term('超参数调优')} - {display_name} - {param_name}", fontsize=14, fontproperties=FONT_PROPERTIES)
    
    # 设置网格
    ax.grid(True, alpha=0.3)
    
    # 标记最佳值
    best_idx = np.argmax(cv_results)
    best_value = param_values[best_idx]
    best_score = cv_results[best_idx]
    
    ax.scatter([best_value], [best_score], s=100, c='r', marker='*', 
              label=f"{translate_term('最佳值')}: {best_value} (Score: {best_score:.4f})")
    
    # 设置图例
    if FONT_PROPERTIES:
        ax.legend(loc='best', fontsize=10, prop=FONT_PROPERTIES)
    else:
        ax.legend(loc='best', fontsize=10)
    
    # 调整布局
    fig.tight_layout()
    
    # 如果是新创建的图形，保存并返回
    if created_fig:
        save_plot(fig, model_name, 'hyperparameter_tuning')
    
    return fig, ax

def plot_model_comparison(model_names, scores, metric_name='AUC', ax=None):
    """
    绘制模型性能比较图
    
    Args:
        model_names: 模型名称列表
        scores: 性能得分列表
        metric_name: 指标名称
        ax: matplotlib轴对象，如果为None则创建新的图形
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    # 将模型名称转换为显示名称
    display_names = [MODEL_DISPLAY_NAMES.get(name, name) for name in model_names]
    
    # 如果没有提供轴对象，创建新的图形
    if ax is None:
        fig, ax = create_figure(figsize=(12, 8))
        created_fig = True
    else:
        fig = ax.figure
        created_fig = False
    
    # 绘制条形图
    bars = ax.bar(range(len(display_names)), scores, width=0.6)
    
    # 添加数据标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
                f'{score:.4f}', ha='center', va='bottom', fontsize=10)
    
    # 设置标签和标题
    ax.set_xlabel(translate_term('模型'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(metric_name, fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_title(f"{translate_term('模型性能比较')} ({metric_name})", fontsize=14, fontproperties=FONT_PROPERTIES)
    
    # 设置x轴刻度
    ax.set_xticks(range(len(display_names)))
    ax.set_xticklabels(display_names, rotation=45, ha='right', fontproperties=FONT_PROPERTIES)
    
    # 设置y轴范围
    min_score = min(scores) * 0.9 if min(scores) > 0 else min(scores) * 1.1
    max_score = max(scores) * 1.1
    ax.set_ylim([min_score, max_score])
    
    # 添加网格
    ax.yaxis.grid(True, alpha=0.3)
    
    # 调整布局
    fig.tight_layout()
    
    # 如果是新创建的图形，保存并返回
    if created_fig:
        save_plot(fig, 'All', 'model_comparison', 'combined_comparison')
    
    return fig, ax

def plot_training_history(history, model_name, ax=None):
    """
    绘制训练历史（如神经网络的训练历史）
    
    Args:
        history: 训练历史，包含'loss'和'val_loss'等键
        model_name: 模型名称
        ax: matplotlib轴对象，如果为None则创建新的图形
        
    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象
    """
    # 获取模型的显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    
    # 如果没有提供轴对象，创建新的图形
    if ax is None:
        fig, ax = create_figure()
        created_fig = True
    else:
        fig = ax.figure
        created_fig = False
    
    # 绘制训练历史
    epochs = range(1, len(history['loss']) + 1)
    
    ax.plot(epochs, history['loss'], 'bo-', label=translate_term('训练损失'))
    if 'val_loss' in history:
        ax.plot(epochs, history['val_loss'], 'ro-', label=translate_term('验证损失'))
    
    # 设置标签和标题
    ax.set_xlabel(translate_term('轮次'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylabel(translate_term('损失'), fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_title(f"{translate_term('训练历史')} - {display_name}", fontsize=14, fontproperties=FONT_PROPERTIES)
    
    # 设置图例和网格
    if FONT_PROPERTIES:
        ax.legend(loc='best', fontsize=10, prop=FONT_PROPERTIES)
    else:
        ax.legend(loc='best', fontsize=10)
    ax.grid(True, alpha=0.3)
    
    # 调整布局
    fig.tight_layout()
    
    # 如果是新创建的图形，保存并返回
    if created_fig:
        save_plot(fig, model_name, 'training_history')
    
    return fig, ax

def plot_roc_comparison(results, title='ROC 对比', ax=None):
    """
    在单个图表上绘制多个模型的ROC曲线以进行比较。

    Args:
        results (dict): 字典，键是模型名称，值是包含 'y_true' 和 'y_pred_proba' 的字典。
        title (str): 图表标题。
        ax (matplotlib.axes.Axes, optional): 要在上面绘图的轴。如果为None，则创建新的图形和轴。

    Returns:
        tuple: (fig, ax) matplotlib图形和轴对象。
    """
    if ax is None:
        fig, ax = create_figure()
    else:
        fig = ax.figure

    colors = sns.color_palette('tab10', len(results))
    
    for (model_name, data), color in zip(results.items(), colors):
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
        
        y_true = data.get('y_true')
        y_pred_proba = data.get('y_pred_proba')

        if y_true is None or y_pred_proba is None:
            logger.warning(f"模型 '{model_name}' 的结果缺少 'y_true' 或 'y_pred_proba'，跳过ROC绘制。")
            continue
            
        from sklearn.metrics import roc_curve, auc
        fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
        auc_val = auc(fpr, tpr)
        
        ax.plot(fpr, tpr, label=f'{display_name} (AUC={auc_val:.3f})', color=color)

    ax.plot([0, 1], [0, 1], 'k--')
    ax.set_title(translate_term(title), fontproperties=get_font_properties(14))
    ax.set_xlabel(translate_term('假阳性率'), fontproperties=get_font_properties(12))
    ax.set_ylabel(translate_term('真阳性率'), fontproperties=get_font_properties(12))
    ax.legend(loc='lower right', prop=get_font_properties(10))
    
    return fig, ax

# 如果直接运行该脚本，执行测试
if __name__ == "__main__":
    # 测试绘图功能
    
    # 生成测试数据
    np.random.seed(42)
    
    # 测试ROC曲线
    fpr = np.linspace(0, 1, 100)
    tpr = np.sqrt(fpr)  # 简单的ROC曲线
    auc_value = 0.75
    
    fig, ax = plot_roc_curve(fpr, tpr, auc_value, 'RandomForest')
    
    # 测试混淆矩阵
    cm = np.array([[85, 15], [20, 80]])
    
    fig, ax = plot_confusion_matrix(cm, 'XGBoost')
    
    # 测试特征重要性
    importance = np.random.rand(15)
    feature_names = [f'Feature {i+1}' for i in range(15)]
    
    fig, ax = plot_feature_importance(importance, feature_names, 'LightGBM')
    
    # 测试学习曲线
    train_sizes = np.linspace(0.1, 1.0, 10)
    train_scores = np.array([np.random.uniform(0.7, 0.9, 5) for _ in range(10)])
    test_scores = np.array([np.random.uniform(0.6, 0.8, 5) for _ in range(10)])
    
    fig, ax = plot_learning_curve(train_sizes, train_scores, test_scores, 'SVM')
    
    # 测试超参数调优
    param_values = [1, 2, 3, 4, 5, 10, 20, 50, 100]
    cv_results = [0.6, 0.65, 0.7, 0.75, 0.8, 0.78, 0.76, 0.75, 0.74]
    
    fig, ax = plot_hyperparameter_search('n_estimators', param_values, cv_results, 'RandomForest')
    
    # 测试模型比较
    model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost']
    scores = [0.75, 0.85, 0.88, 0.87, 0.86]
    
    fig, ax = plot_model_comparison(model_names, scores)
    
    print("测试完成，所有图形已保存到输出目录") 