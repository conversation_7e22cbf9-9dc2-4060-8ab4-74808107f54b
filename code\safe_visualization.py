#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的可视化模块 - 彻底解决字体问题
只使用英文标签，避免所有中文字体相关问题
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 定义保存格式
SAVE_FORMAT = 'pdf'

class SafeVisualization:
    """安全的可视化类，处理各种异常情况"""
    def __init__(self):
        self.setup_safe_matplotlib()

    def setup_safe_matplotlib(self):
        """设置安全的matplotlib配置"""
        try:
            # 重置到默认配置
            plt.rcParams.update(plt.rcParamsDefault)
            
            # 使用最安全的字体设置
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['figure.max_open_warning'] = 0
            
            # 设置字体大小
            plt.rcParams['font.size'] = 10
            plt.rcParams['axes.titlesize'] = 12
            plt.rcParams['axes.labelsize'] = 10
            plt.rcParams['xtick.labelsize'] = 9
            plt.rcParams['ytick.labelsize'] = 9
            plt.rcParams['legend.fontsize'] = 9
            
            # 设置网格样式
            plt.rcParams['axes.grid'] = True
            plt.rcParams['grid.alpha'] = 0.3
            plt.rcParams['axes.axisbelow'] = True
            
            logger.info("Safe matplotlib configuration applied")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup matplotlib: {e}")
            return False

    def safe_ensemble_performance_plot(self, ensemble_results, output_path):
        """
        安全的集成学习性能对比图
        
        Args:
            ensemble_results: 集成学习结果字典
            output_path: 输出路径
        """
        try:
            # 确保matplotlib配置安全
            self.setup_safe_matplotlib()
            
            if not ensemble_results:
                logger.warning("No ensemble results to plot")
                return False
            
            # 提取数据
            methods = []
            metrics_data = {
                'accuracy': [],
                'precision': [],
                'recall': [],
                'f1_score': [],
                'auc': []
            }
            
            for name, result in ensemble_results.items():
                methods.append(name)
                metrics = result.get('metrics', {})
                
                for metric in metrics_data.keys():
                    value = metrics.get(metric, 0.0)
                    metrics_data[metric].append(value)
            
            # 创建子图
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            fig.suptitle('Ensemble Methods Performance Comparison', fontsize=16, fontweight='bold')
            
            # 指标标签（仅英文）
            metrics_labels = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
            metrics_names = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']
            
            # 绘制各个指标的对比图
            for i, (metric, label) in enumerate(zip(metrics_names, metrics_labels)):
                row = i // 3
                col = i % 3
                ax = axes[row, col]
                
                # 绘制柱状图
                bars = ax.bar(methods, metrics_data[metric], alpha=0.7)
                ax.set_title(label, fontweight='bold')
                ax.set_ylabel('Score')
                ax.set_ylim(0, 1)
                
                # 添加数值标签
                for bar, value in zip(bars, metrics_data[metric]):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.3f}', ha='center', va='bottom', fontsize=8)
                
                # 旋转x轴标签
                ax.tick_params(axis='x', rotation=45)
                ax.grid(True, alpha=0.3)
            
            # 隐藏最后一个子图
            axes[1, 2].set_visible(False)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"Ensemble performance plot saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create ensemble performance plot: {e}")
            plt.close('all')  # 确保关闭所有图形
            return False

    def safe_model_comparison_plot(self, model_results, output_path):
        """
        安全的模型对比图
        
        Args:
            model_results: 模型结果字典
            output_path: 输出路径
        """
        try:
            self.setup_safe_matplotlib()
            
            if not model_results:
                logger.warning("No model results to plot")
                return False
            
            # 提取数据
            models = list(model_results.keys())
            f1_scores = [result.get('f1_score', 0) for result in model_results.values()]
            accuracies = [result.get('accuracy', 0) for result in model_results.values()]
            
            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            fig.suptitle('Model Performance Comparison', fontsize=14, fontweight='bold')
            
            # F1分数对比
            bars1 = ax1.bar(models, f1_scores, alpha=0.7, color='skyblue')
            ax1.set_title('F1 Score Comparison')
            ax1.set_ylabel('F1 Score')
            ax1.set_ylim(0, 1)
            ax1.tick_params(axis='x', rotation=45)
            ax1.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, score in zip(bars1, f1_scores):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{score:.3f}', ha='center', va='bottom', fontsize=9)
            
            # 准确率对比
            bars2 = ax2.bar(models, accuracies, alpha=0.7, color='lightcoral')
            ax2.set_title('Accuracy Comparison')
            ax2.set_ylabel('Accuracy')
            ax2.set_ylim(0, 1)
            ax2.tick_params(axis='x', rotation=45)
            ax2.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, score in zip(bars2, accuracies):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{score:.3f}', ha='center', va='bottom', fontsize=9)
            
            plt.tight_layout()
            
            # 保存图片
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"Model comparison plot saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create model comparison plot: {e}")
            plt.close('all')
            return False

    def safe_create_summary_report(self, ensemble_results, output_dir):
        """
        创建安全的总结报告（文本格式）
        
        Args:
            ensemble_results: 集成学习结果
            output_dir: 输出目录
        """
        try:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            report_path = output_dir / "ensemble_summary_report.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("ENSEMBLE LEARNING SUMMARY REPORT\n")
                f.write("=" * 60 + "\n\n")
                
                if not ensemble_results:
                    f.write("No ensemble results available.\n")
                    return True
                
                # 找出最佳模型
                best_model = max(ensemble_results.keys(), 
                               key=lambda x: ensemble_results[x]['metrics'].get('f1_score', 0))
                best_metrics = ensemble_results[best_model]['metrics']
                
                f.write(f"BEST ENSEMBLE MODEL: {best_model}\n")
                f.write(f"Best F1 Score: {best_metrics.get('f1_score', 0):.4f}\n")
                f.write(f"Best Accuracy: {best_metrics.get('accuracy', 0):.4f}\n\n")
                
                f.write("ALL ENSEMBLE MODELS PERFORMANCE:\n")
                f.write("-" * 60 + "\n")
                
                # 按F1分数排序
                sorted_results = sorted(ensemble_results.items(), 
                                      key=lambda x: x[1]['metrics'].get('f1_score', 0), 
                                      reverse=True)
                
                for i, (name, result) in enumerate(sorted_results, 1):
                    metrics = result['metrics']
                    f.write(f"{i}. {name}:\n")
                    f.write(f"   Accuracy:  {metrics.get('accuracy', 0):.4f}\n")
                    f.write(f"   Precision: {metrics.get('precision', 0):.4f}\n")
                    f.write(f"   Recall:    {metrics.get('recall', 0):.4f}\n")
                    f.write(f"   F1 Score:  {metrics.get('f1_score', 0):.4f}\n")
                    f.write(f"   AUC:       {metrics.get('auc', 0):.4f}\n\n")
            
            logger.info(f"Summary report saved to: {report_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create summary report: {e}")
            return False

# 初始化安全配置
safe_visualization = SafeVisualization()
