2025-07-30 21:35:53 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50，数据集类型: small
2025-07-30 21:36:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 171, 'max_depth': 6, 'min_samples_split': 15, 'min_samples_leaf': 9, 'max_features': 'sqrt'}
2025-07-30 21:36:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9891
2025-07-30 21:36:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:36:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\optimization_history_20250730_213624.html
2025-07-30 21:36:25 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:36:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: d:\Code\multi_model_01_updated\output\RandomForest\param_importances_20250730_213625.html
2025-07-30 21:36:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 31.70 秒
2025-07-30 21:37:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:37:00 - hyperparameter_tuning - INFO - 可视化图表已保存为HTML格式到: D:/Code/multi_model_01_updated/output/rf/调优历史.html
2025-07-30 21:37:15 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存PDF失败: 
Image export using the "kaleido" engine requires the Kaleido package,
which can be installed using pip:

    $ pip install --upgrade kaleido

2025-07-30 21:37:15 - hyperparameter_tuning - INFO - 可视化图表已保存为HTML格式到: D:/Code/multi_model_01_updated/output/rf/参数重要性.html
