#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单数据源集成学习模块
实现传统的集成学习方法，包括投票法、装袋法、提升法、堆叠法等
支持单一数据源的多模型集成
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
from joblib import dump, load
from datetime import datetime
from sklearn.base import BaseEstimator, ClassifierMixin, clone
from sklearn.model_selection import cross_val_score, StratifiedKFold, train_test_split
from sklearn.ensemble import (
    VotingClassifier, BaggingClassifier, AdaBoostClassifier, 
    GradientBoostingClassifier, RandomForestClassifier, ExtraTreesClassifier
)
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    roc_curve, auc, matthews_corrcoef
)

# 导入项目模块
from config import OUTPUT_PATH, CACHE_PATH, ENSEMBLE_PATH, RANDOM_SEED
from model_training import MODEL_TRAINERS
from data_preprocessing import load_and_preprocess_data
from logger import get_logger

warnings.filterwarnings('ignore')
logger = get_logger(__name__)

def _get_safe_feature_names(X_data, expected_length):
    """
    安全地获取特征名称，避免字体问题

    Args:
        X_data: 输入数据（可能是DataFrame或numpy数组）
        expected_length: 期望的特征数量

    Returns:
        list: 安全的特征名称列表（英文）
    """
    try:
        # 如果是DataFrame，尝试使用列名
        if hasattr(X_data, 'columns'):
            original_names = X_data.columns.tolist()
            if len(original_names) == expected_length:
                # 将中文特征名转换为安全的英文名称
                safe_names = []
                for i, name in enumerate(original_names):
                    # 如果特征名包含中文或特殊字符，使用英文替代
                    if any(ord(char) > 127 for char in str(name)):
                        safe_names.append(f'Feature_{i}')
                    else:
                        # 保留英文和数字特征名，但确保安全
                        safe_name = str(name).replace(' ', '_').replace('-', '_')
                        safe_names.append(safe_name)
                return safe_names

        # 尝试从缓存加载特征名称
        try:
            # 查找可能的特征名称缓存文件
            cache_files = list(CACHE_PATH.glob("*_feature_names.joblib"))
            if cache_files:
                from joblib import load
                cached_names = load(cache_files[0])  # 使用第一个找到的缓存
                if len(cached_names) == expected_length:
                    # 同样处理缓存的特征名
                    safe_names = []
                    for i, name in enumerate(cached_names):
                        if any(ord(char) > 127 for char in str(name)):
                            safe_names.append(f'Feature_{i}')
                        else:
                            safe_name = str(name).replace(' ', '_').replace('-', '_')
                            safe_names.append(safe_name)
                    return safe_names
        except Exception:
            pass

        # 如果都失败了，使用默认的英文特征名
        return [f'Feature_{i}' for i in range(expected_length)]

    except Exception as e:
        logger.warning(f"获取特征名称失败: {e}，使用默认名称")
        return [f'Feature_{i}' for i in range(expected_length)]

# SHAP 相关导入和可用性检查
try:
    import shap
    SHAP_AVAILABLE = True
    logger.info("SHAP库已成功导入，可解释性分析功能已启用")
except ImportError:
    SHAP_AVAILABLE = False
    logger.warning("SHAP库未安装，可解释性分析功能将被禁用")


class EnsembleClassifier(BaseEstimator, ClassifierMixin):
    """
    集成分类器
    支持多种集成策略：硬投票、软投票、加权投票、Bagging、Boosting、Stacking等
    """
    
    def __init__(self, base_models=None, ensemble_method='voting', 
                 voting='soft', weights=None, meta_classifier=None,
                 n_estimators=10, random_state=None, cv=5):
        """
        初始化集成分类器
        
        Args:
            base_models: 基础模型列表或字典 {model_name: model_instance}
            ensemble_method: 集成方法 ('voting', 'bagging', 'boosting', 'stacking')
            voting: 投票方式 ('hard', 'soft') - 仅用于voting方法
            weights: 模型权重 - 仅用于voting方法
            meta_classifier: 元分类器 - 仅用于stacking方法
            n_estimators: 基础估计器数量 - 用于bagging和boosting
            random_state: 随机种子
            cv: 交叉验证折数 - 用于stacking
        """
        self.base_models = base_models or {}
        self.ensemble_method = ensemble_method
        self.voting = voting
        self.weights = weights
        self.meta_classifier = meta_classifier or LogisticRegression(random_state=random_state)
        self.n_estimators = n_estimators
        self.random_state = random_state or RANDOM_SEED
        self.cv = cv
        
        self.ensemble_model = None
        self.trained_models = {}
        self.feature_importances_ = None
        
    def _prepare_base_models(self):
        """准备基础模型"""
        if isinstance(self.base_models, dict):
            return [(name, model) for name, model in self.base_models.items()]
        elif isinstance(self.base_models, list):
            if all(isinstance(item, tuple) and len(item) == 2 for item in self.base_models):
                return self.base_models
            else:
                return [(f'model_{i}', model) for i, model in enumerate(self.base_models)]
        else:
            raise ValueError("base_models必须是字典或列表格式")
    
    def fit(self, X, y):
        """训练集成模型"""
        logger.info(f"开始训练集成模型，方法: {self.ensemble_method}")
        
        if self.ensemble_method == 'voting':
            self._fit_voting(X, y)
        elif self.ensemble_method == 'bagging':
            self._fit_bagging(X, y)
        elif self.ensemble_method == 'boosting':
            self._fit_boosting(X, y)
        elif self.ensemble_method == 'stacking':
            self._fit_stacking(X, y)
        else:
            raise ValueError(f"不支持的集成方法: {self.ensemble_method}")
        
        return self
    
    def _fit_voting(self, X, y):
        """训练投票集成模型"""
        estimators = self._prepare_base_models()
        
        self.ensemble_model = VotingClassifier(
            estimators=estimators,
            voting=self.voting,
            weights=self.weights
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
        
        # 计算特征重要性（如果可能）
        self._compute_feature_importances()
    
    def _fit_bagging(self, X, y):
        """训练Bagging集成模型"""
        # 使用第一个基础模型作为基础估计器
        if self.base_models:
            base_estimator = list(self.base_models.values())[0]
        else:
            base_estimator = DecisionTreeClassifier(random_state=self.random_state)
        
        self.ensemble_model = BaggingClassifier(
            base_estimator=base_estimator,
            n_estimators=self.n_estimators,
            random_state=self.random_state
        )
        
        self.ensemble_model.fit(X, y)
        
        # 保存基础估计器
        self.trained_models = {
            f'bagging_estimator_{i}': estimator 
            for i, estimator in enumerate(self.ensemble_model.estimators_)
        }
        
        self._compute_feature_importances()
    
    def _fit_boosting(self, X, y):
        """训练Boosting集成模型"""
        # 使用第一个基础模型作为基础估计器
        if self.base_models:
            base_estimator = list(self.base_models.values())[0]
        else:
            base_estimator = DecisionTreeClassifier(max_depth=1, random_state=self.random_state)
        
        self.ensemble_model = AdaBoostClassifier(
            base_estimator=base_estimator,
            n_estimators=self.n_estimators,
            random_state=self.random_state
        )
        
        self.ensemble_model.fit(X, y)
        
        # 保存基础估计器和权重
        self.trained_models = {
            f'boosting_estimator_{i}': estimator 
            for i, estimator in enumerate(self.ensemble_model.estimators_)
        }
        
        self._compute_feature_importances()
    
    def _fit_stacking(self, X, y):
        """训练Stacking集成模型"""
        from sklearn.ensemble import StackingClassifier
        
        estimators = self._prepare_base_models()
        
        self.ensemble_model = StackingClassifier(
            estimators=estimators,
            final_estimator=self.meta_classifier,
            cv=self.cv,
            stack_method='predict_proba' if self.voting == 'soft' else 'predict'
        )
        
        self.ensemble_model.fit(X, y)
        self.trained_models = dict(estimators)
        self.trained_models['meta_classifier'] = self.ensemble_model.final_estimator_
        
        self._compute_feature_importances()
    
    def _compute_feature_importances(self):
        """计算特征重要性"""
        try:
            if hasattr(self.ensemble_model, 'feature_importances_'):
                self.feature_importances_ = self.ensemble_model.feature_importances_
            elif self.ensemble_method == 'voting':
                # 对于投票方法，计算平均特征重要性
                importances = []
                for name, model in self.trained_models.items():
                    if hasattr(model, 'feature_importances_'):
                        importances.append(model.feature_importances_)
                
                if importances:
                    self.feature_importances_ = np.mean(importances, axis=0)
        except Exception as e:
            logger.warning(f"无法计算特征重要性: {e}")
            self.feature_importances_ = None
    
    def predict(self, X):
        """预测"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        return self.ensemble_model.predict(X)
    
    def predict_proba(self, X):
        """预测概率"""
        if self.ensemble_model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        return self.ensemble_model.predict_proba(X)
    
    def score(self, X, y):
        """计算准确率"""
        return self.ensemble_model.score(X, y)


def create_base_models_from_names(model_names, X_train, y_train, X_test, y_test):
    """
    根据模型名称创建并训练基础模型
    
    Args:
        model_names: 模型名称列表
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        
    Returns:
        dict: 训练好的模型字典
    """
    base_models = {}
    
    for model_name in model_names:
        if model_name in MODEL_TRAINERS:
            logger.info(f"训练基础模型: {model_name}")
            try:
                trainer = MODEL_TRAINERS[model_name]
                model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
                base_models[model_name] = model
                logger.info(f"  {model_name} 训练完成")
            except Exception as e:
                logger.error(f"训练模型 {model_name} 失败: {e}")
        else:
            logger.warning(f"未知的模型名称: {model_name}")
    
    return base_models


def evaluate_ensemble_model(ensemble_model, X_test, y_test, model_name="Ensemble"):
    """
    评估集成模型性能
    
    Args:
        ensemble_model: 训练好的集成模型
        X_test, y_test: 测试数据
        model_name: 模型名称
        
    Returns:
        dict: 性能指标字典
    """
    # 预测
    y_pred = ensemble_model.predict(X_test)

    # 安全地获取概率预测
    y_pred_proba = None
    try:
        if hasattr(ensemble_model, 'predict_proba'):
            # 对于硬投票，VotingClassifier可能有predict_proba属性但不能调用
            if hasattr(ensemble_model, 'voting') and ensemble_model.voting == 'hard':
                y_pred_proba = None
            else:
                proba_result = ensemble_model.predict_proba(X_test)
                if proba_result.shape[1] > 1:
                    y_pred_proba = proba_result[:, 1]
                else:
                    y_pred_proba = proba_result.flatten()
    except Exception as e:
        logger.warning(f"无法获取概率预测: {e}")
        y_pred_proba = None
    
    # 计算指标
    metrics = {
        'model_name': model_name,
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
        'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
        'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
        'mcc': matthews_corrcoef(y_test, y_pred)
    }
    
    # 计算AUC（如果可能）
    if y_pred_proba is not None:
        try:
            metrics['auc'] = roc_auc_score(y_test, y_pred_proba)
        except Exception as e:
            logger.warning(f"无法计算AUC: {e}")
            metrics['auc'] = 0.0
    else:
        metrics['auc'] = 0.0
    
    return metrics


def run_ensemble_pipeline(X_train, y_train, X_test, y_test, model_names,
                         ensemble_methods=None, save_results=True,
                         output_dir=None, enable_shap=True):
    """
    运行集成学习管道

    Args:
        X_train, y_train: 训练数据
        X_test, y_test: 测试数据
        model_names: 要使用的基础模型名称列表
        ensemble_methods: 集成方法列表，默认为['voting', 'bagging', 'boosting', 'stacking']
        save_results: 是否保存结果
        output_dir: 输出目录
        enable_shap: 是否启用SHAP分析

    Returns:
        dict: 集成结果字典
    """
    if ensemble_methods is None:
        ensemble_methods = ['voting', 'bagging', 'boosting', 'stacking']

    if output_dir is None:
        output_dir = ENSEMBLE_PATH
    else:
        output_dir = Path(output_dir)

    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("=" * 60)
    logger.info("开始运行集成学习管道")
    logger.info("=" * 60)
    logger.info(f"基础模型: {model_names}")
    logger.info(f"集成方法: {ensemble_methods}")

    # 1. 创建并训练基础模型
    logger.info("步骤1: 训练基础模型")
    base_models = create_base_models_from_names(model_names, X_train, y_train, X_test, y_test)

    if not base_models:
        logger.error("没有成功训练任何基础模型")
        return None

    logger.info(f"成功训练了 {len(base_models)} 个基础模型")

    # 2. 运行不同的集成方法
    ensemble_results = {}

    for method in ensemble_methods:
        logger.info(f"步骤2: 运行集成方法 - {method}")

        try:
            # 创建集成分类器
            if method == 'voting':
                # 尝试软投票和硬投票
                for voting_type in ['soft', 'hard']:
                    ensemble_name = f"{method}_{voting_type}"
                    logger.info(f"  训练 {ensemble_name} 集成模型")

                    ensemble = EnsembleClassifier(
                        base_models=base_models,
                        ensemble_method='voting',
                        voting=voting_type,
                        random_state=RANDOM_SEED
                    )

                    ensemble.fit(X_train, y_train)
                    metrics = evaluate_ensemble_model(ensemble, X_test, y_test, ensemble_name)

                    ensemble_results[ensemble_name] = {
                        'model': ensemble,
                        'metrics': metrics,
                        'method': method,
                        'voting': voting_type
                    }

                    logger.info(f"    {ensemble_name} - 准确率: {metrics['accuracy']:.4f}, F1: {metrics['f1_score']:.4f}")

            else:
                ensemble = EnsembleClassifier(
                    base_models=base_models,
                    ensemble_method=method,
                    random_state=RANDOM_SEED
                )

                ensemble.fit(X_train, y_train)
                metrics = evaluate_ensemble_model(ensemble, X_test, y_test, method)

                ensemble_results[method] = {
                    'model': ensemble,
                    'metrics': metrics,
                    'method': method
                }

                logger.info(f"  {method} - 准确率: {metrics['accuracy']:.4f}, F1: {metrics['f1_score']:.4f}")

        except Exception as e:
            logger.error(f"集成方法 {method} 失败: {e}")

    # 3. 找出最佳集成模型
    if ensemble_results:
        best_model_name = max(ensemble_results.keys(),
                            key=lambda x: ensemble_results[x]['metrics']['f1_score'])
        best_model_info = ensemble_results[best_model_name]

        logger.info("=" * 60)
        logger.info("集成学习结果总结")
        logger.info("=" * 60)
        logger.info(f"最佳集成模型: {best_model_name}")
        logger.info(f"最佳F1分数: {best_model_info['metrics']['f1_score']:.4f}")
        logger.info(f"最佳准确率: {best_model_info['metrics']['accuracy']:.4f}")

        # 显示所有模型的性能对比
        logger.info("\n所有集成模型性能对比:")
        for name, result in ensemble_results.items():
            metrics = result['metrics']
            logger.info(f"  {name:15} - 准确率: {metrics['accuracy']:.4f}, "
                       f"精确率: {metrics['precision']:.4f}, "
                       f"召回率: {metrics['recall']:.4f}, "
                       f"F1: {metrics['f1_score']:.4f}, "
                       f"AUC: {metrics['auc']:.4f}")

    # 4. 保存结果
    if save_results and ensemble_results:
        logger.info("步骤3: 保存集成学习结果")

        # 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = output_dir / f"ensemble_results_{timestamp}.joblib"

        # 准备保存的数据
        save_data = {
            'ensemble_results': ensemble_results,
            'base_models': base_models,
            'best_model': best_model_name if ensemble_results else None,
            'timestamp': timestamp,
            'model_names': model_names,
            'ensemble_methods': ensemble_methods
        }

        dump(save_data, results_file)
        logger.info(f"集成学习结果已保存到: {results_file}")

        # 保存性能报告
        save_ensemble_report(ensemble_results, output_dir / f"ensemble_report_{timestamp}.html")

    # 5. SHAP分析（如果启用）
    if enable_shap and SHAP_AVAILABLE and ensemble_results:
        logger.info("步骤4: 生成SHAP可解释性分析")
        try:
            generate_ensemble_shap_analysis(
                ensemble_results, X_test, y_test,
                output_dir / "shap_analysis"
            )
        except Exception as e:
            logger.warning(f"SHAP分析失败: {e}")

    return ensemble_results


def save_ensemble_report(ensemble_results, output_file):
    """
    保存集成学习HTML报告 - 使用安全的字体配置

    Args:
        ensemble_results: 集成结果字典
        output_file: 输出文件路径
    """
    try:
        # 使用安全的文本报告替代HTML报告，避免字体问题
        from safe_visualization import safe_create_summary_report

        output_path = Path(output_file).parent
        safe_create_summary_report(ensemble_results, output_path)

        logger.info(f"Ensemble report saved to: {output_path}")

    except Exception as e:
        logger.warning(f"Failed to save ensemble report: {e}")
        # 降级处理：生成简单的文本报告
        try:
            _save_simple_text_report(ensemble_results, output_file)
        except:
            pass

def _save_simple_text_report(ensemble_results, output_file):
    """生成简单的文本报告"""
    output_file = Path(output_file).with_suffix('.txt')

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("ENSEMBLE LEARNING PERFORMANCE REPORT\n")
        f.write("=" * 50 + "\n\n")

        if not ensemble_results:
            f.write("No ensemble results available.\n")
            return

        # 找出最佳模型
        best_model = max(ensemble_results.keys(),
                        key=lambda x: ensemble_results[x]['metrics'].get('f1_score', 0))
        best_metrics = ensemble_results[best_model]['metrics']

        f.write(f"BEST ENSEMBLE MODEL: {best_model}\n")
        f.write(f"Best F1 Score: {best_metrics.get('f1_score', 0):.4f}\n")
        f.write(f"Best Accuracy: {best_metrics.get('accuracy', 0):.4f}\n\n")

        f.write("ALL ENSEMBLE MODELS PERFORMANCE:\n")
        f.write("-" * 50 + "\n")

        for i, (name, result) in enumerate(ensemble_results.items(), 1):
            metrics = result['metrics']
            f.write(f"{i}. {name}:\n")
            f.write(f"   Accuracy:  {metrics.get('accuracy', 0):.4f}\n")
            f.write(f"   Precision: {metrics.get('precision', 0):.4f}\n")
            f.write(f"   Recall:    {metrics.get('recall', 0):.4f}\n")
            f.write(f"   F1 Score:  {metrics.get('f1_score', 0):.4f}\n")
            f.write(f"   AUC:       {metrics.get('auc', 0):.4f}\n\n")


def generate_ensemble_shap_analysis(ensemble_results, X_test, y_test, output_dir):
    """
    生成集成模型的SHAP可解释性分析

    Args:
        ensemble_results: 集成结果字典
        X_test, y_test: 测试数据
        output_dir: 输出目录
    """
    if not SHAP_AVAILABLE:
        logger.warning("SHAP库不可用，跳过可解释性分析")
        return

    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 为每个集成模型生成SHAP分析
    for name, result in ensemble_results.items():
        try:
            logger.info(f"为 {name} 生成SHAP分析")

            model = result['model']
            model_output_dir = output_dir / name
            model_output_dir.mkdir(parents=True, exist_ok=True)

            # 使用增强的SHAP可视化
            try:
                from enhanced_shap_visualization import create_complete_shap_analysis

                # 获取真实的特征名称
                real_feature_names = _get_safe_feature_names(X_test, X_test.shape[1])

                # 创建完整的SHAP分析
                shap_results = create_complete_shap_analysis(
                    model=model.ensemble_model,
                    X_data=X_test,
                    feature_names=real_feature_names,
                    model_name=name,
                    output_dir=model_output_dir,
                    max_samples=100
                )

                if shap_results:
                    logger.info(f"  {name} 完整SHAP分析完成")
                    logger.info(f"    生成的图表类型: {list(shap_results.keys())}")

                    # 记录生成的文件
                    for plot_type, paths in shap_results.items():
                        if isinstance(paths, list):
                            logger.info(f"    {plot_type}: {len(paths)} 个文件")
                        elif isinstance(paths, dict):
                            logger.info(f"    {plot_type}: {list(paths.keys())}")
                        elif paths:
                            logger.info(f"    {plot_type}: 1 个文件")
                else:
                    logger.warning(f"  {name} SHAP分析未生成任何图表")

            except ImportError:
                logger.warning("增强SHAP可视化模块不可用，使用基础SHAP分析")
                # 回退到基础SHAP分析
                _create_basic_shap_analysis(model.ensemble_model, X_test, name, model_output_dir)
            except Exception as e:
                logger.warning(f"增强SHAP分析失败: {e}，尝试基础分析")
                _create_basic_shap_analysis(model.ensemble_model, X_test, name, model_output_dir)

            logger.info(f"  {name} SHAP分析完成")

        except Exception as e:
            logger.warning(f"为 {name} 生成SHAP分析失败: {e}")

def _create_basic_shap_analysis(model, X_test, name, output_dir):
    """创建基础的SHAP分析（回退方案）"""
    try:
        # 创建SHAP解释器
        if hasattr(model, 'predict_proba'):
            explainer = shap.Explainer(model.predict_proba, X_test[:50])
        else:
            explainer = shap.Explainer(model.predict, X_test[:50])

        # 计算SHAP值
        shap_values = explainer(X_test[:50])

        # 获取特征名称
        real_feature_names = _get_safe_feature_names(X_test, X_test.shape[1])

        # 生成基础摘要图
        plt.figure(figsize=(10, 6))
        shap.summary_plot(shap_values, X_test[:50], feature_names=real_feature_names, show=False)
        plt.title(f'{name} - SHAP Summary Plot')
        plt.tight_layout()
        
        # 保存图表
        output_dir.mkdir(parents=True, exist_ok=True)
        plt.savefig(output_dir / f'shap_summary.{SAVE_FORMAT}', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 生成特征重要性图
        if hasattr(shap_values, 'values'):
            feature_importance = np.abs(shap_values.values).mean(0)
            if len(feature_importance.shape) > 1:
                feature_importance = feature_importance[:, 1]

            plt.figure(figsize=(10, 6))
            sorted_idx = np.argsort(feature_importance)[-10:]

            plt.barh(range(len(sorted_idx)), feature_importance[sorted_idx])
            plt.yticks(range(len(sorted_idx)), [real_feature_names[i] for i in sorted_idx])
            plt.xlabel('SHAP Feature Importance')
            plt.title(f'{name} - Top 10 Feature Importance')
            plt.tight_layout()
            
            # 保存图表
            plt.savefig(output_dir / f'feature_importance.{SAVE_FORMAT}', dpi=300, bbox_inches='tight')
            plt.close()

        logger.info(f"  {name} 基础SHAP分析完成")

    except Exception as e:
        logger.warning(f"基础SHAP分析也失败: {e}")

def load_ensemble_results(results_file):
    """
    加载集成学习结果

    Args:
        results_file: 结果文件路径

    Returns:
        dict: 集成结果字典
    """
    try:
        results = load(results_file)
        logger.info(f"成功加载集成学习结果: {results_file}")
        return results
    except Exception as e:
        logger.error(f"加载集成学习结果失败: {e}")
        return None
