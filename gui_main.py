#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模型集成机器学习平台 - GUI主界面
提供完整的图形用户界面，实现所有命令行功能的可视化操作
"""

import sys
import os
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import json
from datetime import datetime

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

# 导入项目模块
try:
    from config import MODEL_NAMES, MODEL_DISPLAY_NAMES, OUTPUT_PATH, CACHE_PATH, ENSEMBLE_CONFIG
    from logger import get_logger
    from data_preprocessing import DataPreprocessor, load_and_preprocess_data
    from model_training import MODEL_TRAINERS
    from plot_utils import PlotManager
    from model_ensemble import run_ensemble_pipeline
    # from plot_ensemble import visualize_ensemble_results  # 使用安全的可视化模块替代
    from multi_data_ensemble import run_multi_data_ensemble_pipeline
    from external_validation import run_external_validation
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的环境中运行GUI程序")
    sys.exit(1)

# 定义保存格式
SAVE_FORMAT = 'pdf'  # 默认保存为PDF格式以获得更好的打印质量

class MLPlatformGUI:
    """
    多模型集成机器学习平台GUI主类
    """
    
    def __init__(self):
        """初始化GUI界面"""
        self.root = tk.Tk()
        self.root.title("多模型集成机器学习平台")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.current_data_path = tk.StringVar()
        self.selected_models = []
        self.training_progress = tk.DoubleVar()
        self.status_text = tk.StringVar(value="就绪")
        
        # 初始化组件
        self.logger = get_logger("GUI")
        self.data_preprocessor = DataPreprocessor()
        self.plot_manager = PlotManager()
        
        # 线程通信队列
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()

        # 初始化定时器管理
        self.timer_ids = []
        self.is_running = True

        # 创建界面
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()

        # 初始化GUI功能模块
        from gui_functions import GUIFunctions
        self.functions = GUIFunctions(self)

        # 启动定时器检查任务结果
        self.check_results()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.logger.info("GUI界面初始化完成")
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载数据...", command=self.load_data, accelerator="Ctrl+O")
        file_menu.add_command(label="保存项目...", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="加载项目...", command=self.load_project, accelerator="Ctrl+L")
        file_menu.add_separator()
        file_menu.add_command(label="导出结果...", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.quit_app, accelerator="Ctrl+Q")
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="清除缓存", command=self.clear_cache)
        edit_menu.add_command(label="重置设置", command=self.reset_settings)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="显示日志", command=self.toggle_log_panel)
        view_menu.add_command(label="全屏", command=self.toggle_fullscreen)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据质量检查", command=self.data_quality_check)
        tools_menu.add_command(label="模型选择建议", command=self.model_selection_advisor)
        tools_menu.add_command(label="配置生成器", command=self.config_generator)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
        # 绑定快捷键
        self.root.bind('<Control-o>', lambda e: self.load_data())
        self.root.bind('<Control-s>', lambda e: self.save_project())
        self.root.bind('<Control-l>', lambda e: self.load_project())
        self.root.bind('<Control-q>', lambda e: self.quit_app())
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # 数据操作按钮
        ttk.Button(toolbar, text="📁 加载数据", command=self.load_data).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 模型操作按钮
        ttk.Button(toolbar, text="🚀 训练模型", command=self.train_models).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📊 可视化", command=self.visualize_results).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📈 比较模型", command=self.compare_models).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # 高级功能按钮
        ttk.Button(toolbar, text="🎯 最佳模型", command=self.select_best_model).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🚀 完整分析", command=self.run_complete_analysis).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🔧 超参数调优", command=self.hyperparameter_tuning).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📋 生成报告", command=self.generate_report).pack(side=tk.LEFT, padx=2)
        
        # 右侧状态显示
        ttk.Label(toolbar, text="当前数据:").pack(side=tk.RIGHT, padx=5)
        ttk.Label(toolbar, textvariable=self.current_data_path, width=30).pack(side=tk.RIGHT)
    
    def create_main_layout(self):
        """创建主要布局"""
        # 创建主要的PanedWindow
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧导航面板
        self.create_navigation_panel(main_paned)
        
        # 中央工作区域
        self.create_work_area(main_paned)
        
        # 右侧配置面板
        self.create_config_panel(main_paned)
    
    def create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        nav_frame = ttk.LabelFrame(parent, text="功能导航", width=250)
        parent.add(nav_frame, weight=1)
        
        # 创建树形导航
        self.nav_tree = ttk.Treeview(nav_frame, show="tree")
        self.nav_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加导航项目
        data_node = self.nav_tree.insert("", "end", text="📁 数据管理", open=True)
        self.nav_tree.insert(data_node, "end", text="加载数据", tags=("load_data",))
        self.nav_tree.insert(data_node, "end", text="数据预览", tags=("preview_data",))
        self.nav_tree.insert(data_node, "end", text="数据验证", tags=("validate_data",))
        self.nav_tree.insert(data_node, "end", text="数据预处理", tags=("preprocess_data",))
        self.nav_tree.insert(data_node, "end", text="数据探索", tags=("data_exploration",))
        
        model_node = self.nav_tree.insert("", "end", text="🤖 模型训练", open=True)
        self.nav_tree.insert(model_node, "end", text="模型选择", tags=("select_models",))
        self.nav_tree.insert(model_node, "end", text="参数配置", tags=("config_params",))
        self.nav_tree.insert(model_node, "end", text="开始训练", tags=("start_training",))
        self.nav_tree.insert(model_node, "end", text="超参数调优", tags=("hyperparameter_tuning",))
        
        viz_node = self.nav_tree.insert("", "end", text="📊 结果可视化", open=True)
        self.nav_tree.insert(viz_node, "end", text="单模型可视化", tags=("single_viz",))
        self.nav_tree.insert(viz_node, "end", text="模型比较", tags=("compare_viz",))
        self.nav_tree.insert(viz_node, "end", text="性能报告", tags=("performance_report",))
        
        ensemble_node = self.nav_tree.insert("", "end", text="🤝 集成学习", open=True)
        self.nav_tree.insert(ensemble_node, "end", text="单数据源集成", tags=("single_ensemble",))
        self.nav_tree.insert(ensemble_node, "end", text="多数据源集成", tags=("multi_ensemble",))
        self.nav_tree.insert(ensemble_node, "end", text="外部验证", tags=("external_validation",))
        
        # 绑定点击事件
        self.nav_tree.bind("<<TreeviewSelect>>", self.on_nav_select)
    
    def create_work_area(self, parent):
        """创建中央工作区域"""
        work_frame = ttk.LabelFrame(parent, text="工作区域")
        parent.add(work_frame, weight=3)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(work_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 数据管理选项卡
        self.create_data_tab()
        
        # 模型训练选项卡
        self.create_training_tab()
        
        # 结果可视化选项卡
        self.create_visualization_tab()
        
        # 集成学习选项卡
        self.create_ensemble_tab()

        # 初始化并创建数据探索选项卡
        from gui_data_exploration import DataExplorationGUI
        self.data_exploration = DataExplorationGUI(self)
        self.data_exploration.create_exploration_tab(self.notebook)
    
    def create_config_panel(self, parent):
        """创建右侧配置面板"""
        config_frame = ttk.LabelFrame(parent, text="配置与日志", width=300)
        parent.add(config_frame, weight=1)
        
        # 创建配置和日志的选项卡
        config_notebook = ttk.Notebook(config_frame)
        config_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 配置选项卡
        config_tab = ttk.Frame(config_notebook)
        config_notebook.add(config_tab, text="配置")
        
        # 日志选项卡
        log_tab = ttk.Frame(config_notebook)
        config_notebook.add(log_tab, text="日志")
        
        # 创建日志显示区域
        self.log_text = tk.Text(log_tab, wrap=tk.WORD, height=20)
        log_scrollbar = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态文本
        ttk.Label(status_frame, textvariable=self.status_text).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.training_progress, 
                                          maximum=100, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 时间显示
        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        if not self.is_running:
            return
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.config(text=current_time)
            timer_id = self.root.after(1000, self.update_time)
            self.timer_ids.append(timer_id)
        except tk.TclError:
            # GUI已关闭，停止定时器
            self.is_running = False
    
    def create_data_tab(self):
        """创建数据管理选项卡"""
        data_tab = ttk.Frame(self.notebook)
        self.notebook.add(data_tab, text="📁 数据管理")

        # 数据加载区域
        load_frame = ttk.LabelFrame(data_tab, text="数据加载")
        load_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(load_frame, text="数据文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.data_path_entry = ttk.Entry(load_frame, textvariable=self.current_data_path, width=50)
        self.data_path_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(load_frame, text="浏览...", command=self.browse_data_file).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(load_frame, text="加载", command=self.load_data_file).grid(row=0, column=3, padx=5, pady=5)

        # 数据预览区域
        preview_frame = ttk.LabelFrame(data_tab, text="数据预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建表格显示数据
        self.data_tree = ttk.Treeview(preview_frame, show="headings")
        data_scrollbar_y = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        data_scrollbar_x = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=data_scrollbar_y.set, xscrollcommand=data_scrollbar_x.set)

        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        data_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 数据信息显示
        info_frame = ttk.LabelFrame(data_tab, text="数据信息")
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        self.data_info_text = tk.Text(info_frame, height=6, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.data_info_text.yview)
        self.data_info_text.configure(yscrollcommand=info_scrollbar.set)

        self.data_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_training_tab(self):
        """创建模型训练选项卡"""
        training_tab = ttk.Frame(self.notebook)
        self.notebook.add(training_tab, text="🤖 模型训练")

        # 模型选择区域
        model_frame = ttk.LabelFrame(training_tab, text="模型选择")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建模型复选框
        self.model_vars = {}
        row, col = 0, 0
        for model_name in MODEL_NAMES:
            display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
            var = tk.BooleanVar()
            self.model_vars[model_name] = var

            cb = ttk.Checkbutton(model_frame, text=display_name, variable=var)
            cb.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)

            col += 1
            if col >= 3:  # 每行3个
                col = 0
                row += 1

        # 全选/取消全选按钮
        button_frame = ttk.Frame(model_frame)
        button_frame.grid(row=row+1, column=0, columnspan=3, pady=5)
        ttk.Button(button_frame, text="全选", command=self.select_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消全选", command=self.deselect_all_models).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="推荐选择", command=self.recommend_models).pack(side=tk.LEFT, padx=5)

        # 训练参数配置区域
        param_frame = ttk.LabelFrame(training_tab, text="训练参数")
        param_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(param_frame, text="测试集比例:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.test_size_var = tk.DoubleVar(value=0.2)
        test_size_scale = ttk.Scale(param_frame, from_=0.1, to=0.5, variable=self.test_size_var, orient=tk.HORIZONTAL)
        test_size_scale.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        self.test_size_label = ttk.Label(param_frame, text="0.2")
        self.test_size_label.grid(row=0, column=2, padx=5, pady=5)
        test_size_scale.configure(command=self.update_test_size_label)

        ttk.Label(param_frame, text="随机种子:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.random_seed_var = tk.IntVar(value=42)
        ttk.Entry(param_frame, textvariable=self.random_seed_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(param_frame, text="特征缩放:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.scaling_var = tk.StringVar(value="standard")
        scaling_combo = ttk.Combobox(param_frame, textvariable=self.scaling_var,
                                   values=["standard", "minmax", "robust", "none"], state="readonly")
        scaling_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # 超参数调优配置区域
        tuning_frame = ttk.LabelFrame(training_tab, text="超参数调优配置")
        tuning_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行：调优开关和试验次数
        row1_frame = ttk.Frame(tuning_frame)
        row1_frame.pack(fill=tk.X, padx=5, pady=5)

        self.enable_tuning_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(row1_frame, text="启用超参数调优", variable=self.enable_tuning_var,
                       command=self.toggle_tuning_options).pack(side=tk.LEFT, padx=5)

        ttk.Label(row1_frame, text="试验次数:").pack(side=tk.LEFT, padx=(20, 5))
        self.n_trials_var = tk.IntVar(value=50)
        trials_spinbox = ttk.Spinbox(row1_frame, from_=10, to=200, textvariable=self.n_trials_var, width=10)
        trials_spinbox.pack(side=tk.LEFT, padx=5)

        # 第二行：调优策略和超时设置
        row2_frame = ttk.Frame(tuning_frame)
        row2_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(row2_frame, text="调优模式:").pack(side=tk.LEFT, padx=5)
        self.tuning_strategy_var = tk.StringVar(value="standard")
        strategy_combo = ttk.Combobox(row2_frame, textvariable=self.tuning_strategy_var,
                                    values=["quick", "standard", "deep"], state="readonly", width=10)
        strategy_combo.pack(side=tk.LEFT, padx=5)
        
        # 添加提示信息
        strategy_tip = ttk.Label(row2_frame, text="quick: 10次试验, standard: 50次, deep: 100次", font=("", 8))
        strategy_tip.pack(side=tk.LEFT, padx=5)

        ttk.Label(row2_frame, text="超时(分钟):").pack(side=tk.LEFT, padx=(20, 5))
        self.tuning_timeout_var = tk.IntVar(value=30)
        timeout_spinbox = ttk.Spinbox(row2_frame, from_=5, to=120, textvariable=self.tuning_timeout_var, width=10)
        timeout_spinbox.pack(side=tk.LEFT, padx=5)

        # 第三行：并行设置和评估指标
        row3_frame = ttk.Frame(tuning_frame)
        row3_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(row3_frame, text="并行作业数:").pack(side=tk.LEFT, padx=5)
        self.tuning_n_jobs_var = tk.IntVar(value=1)
        jobs_spinbox = ttk.Spinbox(row3_frame, from_=1, to=8, textvariable=self.tuning_n_jobs_var, width=10)
        jobs_spinbox.pack(side=tk.LEFT, padx=5)

        ttk.Label(row3_frame, text="评估指标:").pack(side=tk.LEFT, padx=(20, 5))
        self.tuning_metric_var = tk.StringVar(value="roc_auc")
        metric_combo = ttk.Combobox(row3_frame, textvariable=self.tuning_metric_var,
                                  values=["roc_auc", "accuracy", "f1", "precision", "recall"],
                                  state="readonly", width=12)
        metric_combo.pack(side=tk.LEFT, padx=5)

        # 训练控制区域
        control_frame = ttk.LabelFrame(training_tab, text="训练控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="🚀 开始训练", command=self.start_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="⏹ 停止训练", command=self.stop_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="🔧 超参数调优", command=self.start_hyperparameter_tuning).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(control_frame, text="📊 调优结果", command=self.show_tuning_results).pack(side=tk.LEFT, padx=5, pady=5)

        # 训练进度显示
        progress_frame = ttk.LabelFrame(training_tab, text="训练进度")
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.training_log = tk.Text(progress_frame, wrap=tk.WORD)
        training_scrollbar = ttk.Scrollbar(progress_frame, orient=tk.VERTICAL, command=self.training_log.yview)
        self.training_log.configure(yscrollcommand=training_scrollbar.set)

        self.training_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        training_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_visualization_tab(self):
        """创建结果可视化选项卡"""
        viz_tab = ttk.Frame(self.notebook)
        self.notebook.add(viz_tab, text="📊 结果可视化")

        # 可视化控制区域
        control_frame = ttk.LabelFrame(viz_tab, text="可视化控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(control_frame, text="选择模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.viz_model_var = tk.StringVar()
        self.viz_model_combo = ttk.Combobox(control_frame, textvariable=self.viz_model_var,
                                          values=MODEL_NAMES, state="readonly")
        self.viz_model_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Button(control_frame, text="📈 单模型可视化", command=self.single_model_visualization).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(control_frame, text="📊 模型比较", command=self.model_comparison).grid(row=0, column=3, padx=5, pady=5)
        ttk.Button(control_frame, text="📋 生成报告", command=self.generate_performance_report).grid(row=0, column=4, padx=5, pady=5)

        # 图表显示区域
        chart_frame = ttk.LabelFrame(viz_tab, text="图表显示")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 这里将嵌入matplotlib图表
        self.chart_canvas = None  # 将在需要时创建

        # 图表选项
        options_frame = ttk.Frame(chart_frame)
        options_frame._is_options_frame = True  # 标识选项框架
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(options_frame, text="图表类型:").pack(side=tk.LEFT, padx=5)
        self.chart_type_var = tk.StringVar(value="ROC曲线")
        chart_type_combo = ttk.Combobox(options_frame, textvariable=self.chart_type_var,
                                      values=["ROC曲线", "混淆矩阵", "特征重要性", "学习曲线", "性能比较", "SHAP分析"],
                                      state="readonly")
        chart_type_combo.pack(side=tk.LEFT, padx=5)

        ttk.Button(options_frame, text="刷新图表", command=self.refresh_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(options_frame, text="保存图表", command=self.save_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(options_frame, text="详细报告", command=self.generate_detailed_report).pack(side=tk.LEFT, padx=5)

    def create_ensemble_tab(self):
        """创建集成学习选项卡"""
        ensemble_tab = ttk.Frame(self.notebook)
        self.notebook.add(ensemble_tab, text="🤝 集成学习")

        # 创建主要的框架
        left_frame = ttk.Frame(ensemble_tab)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        right_frame = ttk.Frame(ensemble_tab)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 单数据源集成学习配置
        single_frame = ttk.LabelFrame(left_frame, text="单数据源集成学习")
        single_frame.pack(fill=tk.X, pady=5)

        # 模型选择（用于单数据源集成）
        model_select_frame = ttk.Frame(single_frame)
        model_select_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(model_select_frame, text="选择模型进行集成:").pack(anchor=tk.W)

        # 创建模型选择的复选框
        model_grid_frame = ttk.Frame(model_select_frame)
        model_grid_frame.pack(fill=tk.X, pady=5)

        # 使用现有的model_vars，如果不存在则创建
        if not hasattr(self, 'model_vars'):
            self.model_vars = {}
            for model in MODEL_NAMES:
                self.model_vars[model] = tk.BooleanVar()

        # 创建3列的模型选择网格
        for i, model in enumerate(MODEL_NAMES):
            row = i // 3
            col = i % 3
            display_name = MODEL_DISPLAY_NAMES.get(model, model)
            ttk.Checkbutton(model_grid_frame, text=display_name,
                          variable=self.model_vars[model]).grid(row=row, column=col,
                                                               sticky=tk.W, padx=10, pady=2)

        # 集成方法选择
        method_frame = ttk.LabelFrame(single_frame, text="集成方法")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        self.ensemble_methods = {}
        methods = [
            ("voting", "投票法"),
            ("bagging", "装袋法"),
            ("boosting", "提升法"),
            ("stacking", "堆叠法")
        ]

        method_grid_frame = ttk.Frame(method_frame)
        method_grid_frame.pack(fill=tk.X, pady=5)

        for i, (method, display_name) in enumerate(methods):
            var = tk.BooleanVar(value=(method in ['voting', 'stacking']))  # 默认选择常用方法
            self.ensemble_methods[method] = var
            row = i // 2
            col = i % 2
            ttk.Checkbutton(method_grid_frame, text=display_name,
                          variable=var).grid(row=row, column=col, sticky=tk.W, padx=10, pady=2)

        # 智能模型选择配置
        smart_select_frame = ttk.LabelFrame(single_frame, text="智能模型选择")
        smart_select_frame.pack(fill=tk.X, padx=5, pady=5)

        # 选择策略
        strategy_frame = ttk.Frame(smart_select_frame)
        strategy_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(strategy_frame, text="选择策略:").pack(side=tk.LEFT)
        self.selection_strategy_var = tk.StringVar(value="balanced")
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.selection_strategy_var,
                                    values=["performance", "diversity", "balanced", "quantified"],
                                    state="readonly", width=15)
        strategy_combo.pack(side=tk.LEFT, padx=5)

        # 添加策略说明
        ttk.Label(strategy_frame, text="(quantified=量化多样性评估)",
                 font=("Arial", 8)).pack(side=tk.LEFT, padx=5)

        # 目标模型数量
        ttk.Label(strategy_frame, text="目标数量:").pack(side=tk.LEFT, padx=(10,0))
        self.target_size_var = tk.IntVar(value=3)
        ttk.Spinbox(strategy_frame, from_=2, to=8, textvariable=self.target_size_var,
                   width=5).pack(side=tk.LEFT, padx=5)

        # 阈值设置
        threshold_frame = ttk.Frame(smart_select_frame)
        threshold_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(threshold_frame, text="相关性阈值:").pack(side=tk.LEFT)
        self.correlation_threshold_var = tk.DoubleVar(value=0.3)
        ttk.Entry(threshold_frame, textvariable=self.correlation_threshold_var,
                 width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(threshold_frame, text="最低性能:").pack(side=tk.LEFT, padx=(10,0))
        self.min_performance_var = tk.DoubleVar(value=0.6)
        ttk.Entry(threshold_frame, textvariable=self.min_performance_var,
                 width=8).pack(side=tk.LEFT, padx=5)

        # 单数据源集成控制按钮
        single_control_frame = ttk.Frame(single_frame)
        single_control_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(single_control_frame, text="🎯 智能模型选择",
                  command=self.smart_model_selection).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="🚀 开始单数据源集成",
                  command=self.start_ensemble).pack(side=tk.LEFT, padx=5)
        ttk.Button(single_control_frame, text="📊 集成可视化",
                  command=self.ensemble_visualization).pack(side=tk.LEFT, padx=5)

        # 多数据源集成学习配置
        multi_frame = ttk.LabelFrame(left_frame, text="多数据源集成学习")
        multi_frame.pack(fill=tk.X, pady=5)

        # 配置文件选择
        config_frame = ttk.Frame(multi_frame)
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(config_frame, text="配置文件:").pack(anchor=tk.W)
        config_input_frame = ttk.Frame(config_frame)
        config_input_frame.pack(fill=tk.X, pady=2)

        self.config_path_var = tk.StringVar()
        ttk.Entry(config_input_frame, textvariable=self.config_path_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(config_input_frame, text="浏览...", command=self.browse_config_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(config_input_frame, text="生成配置", command=self.generate_config).pack(side=tk.LEFT, padx=5)

        # 数据策略选择
        strategy_frame = ttk.LabelFrame(multi_frame, text="数据策略")
        strategy_frame.pack(fill=tk.X, padx=5, pady=5)

        self.data_strategies = {}
        strategies = [
            ("unified", "统一数据"),
            ("original", "原始数据"),
            ("combined", "组合数据")
        ]

        strategy_grid_frame = ttk.Frame(strategy_frame)
        strategy_grid_frame.pack(fill=tk.X, pady=5)

        for i, (strategy, display_name) in enumerate(strategies):
            var = tk.BooleanVar(value=(strategy == 'unified'))  # 默认选择unified
            self.data_strategies[strategy] = var
            ttk.Checkbutton(strategy_grid_frame, text=display_name,
                          variable=var).grid(row=0, column=i, sticky=tk.W, padx=10, pady=2)

        # 特征选择配置（用于多数据源）
        feature_frame = ttk.LabelFrame(multi_frame, text="特征选择")
        feature_frame.pack(fill=tk.X, padx=5, pady=5)

        feature_config_frame = ttk.Frame(feature_frame)
        feature_config_frame.pack(fill=tk.X, padx=5, pady=5)

        self.feature_selection_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(feature_config_frame, text="启用特征选择",
                       variable=self.feature_selection_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)

        ttk.Label(feature_config_frame, text="选择方法:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.feature_method_var = tk.StringVar(value="weighted")
        method_combo = ttk.Combobox(feature_config_frame, textvariable=self.feature_method_var,
                                  values=["weighted", "combined", "union", "intersection", "meta_model"],
                                  state="readonly", width=15)
        method_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(feature_config_frame, text="特征数量:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.feature_k_var = tk.IntVar(value=10)
        ttk.Entry(feature_config_frame, textvariable=self.feature_k_var, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        # 多数据源集成控制按钮
        multi_control_frame = ttk.Frame(multi_frame)
        multi_control_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(multi_control_frame, text="🔗 开始多数据源集成",
                  command=self.start_multi_data_ensemble).pack(side=tk.LEFT, padx=5)

        # 其他功能控制
        other_frame = ttk.LabelFrame(left_frame, text="其他功能")
        other_frame.pack(fill=tk.X, pady=5)

        other_control_frame = ttk.Frame(other_frame)
        other_control_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(other_control_frame, text="� 外部验证",
                  command=self.external_validation).pack(side=tk.LEFT, padx=5)
        ttk.Button(other_control_frame, text="� 查看结果",
                  command=self.view_ensemble_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(other_control_frame, text="🔗 打开输出目录",
                  command=self.open_output_directory).pack(side=tk.LEFT, padx=5)

        # 右侧：进度显示和结果显示
        # 进度显示
        progress_frame = ttk.LabelFrame(right_frame, text="执行进度")
        progress_frame.pack(fill=tk.X, pady=5)

        self.ensemble_progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.ensemble_progress.pack(fill=tk.X, padx=5, pady=5)

        self.ensemble_status_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.ensemble_status_var).pack(pady=2)

        # 集成结果显示
        result_frame = ttk.LabelFrame(right_frame, text="集成结果与日志")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建带标签页的结果显示
        result_notebook = ttk.Notebook(result_frame)
        result_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 结果标签页
        result_tab = ttk.Frame(result_notebook)
        result_notebook.add(result_tab, text="执行结果")

        self.ensemble_result_text = tk.Text(result_tab, wrap=tk.WORD, font=('Consolas', 9))
        result_scrollbar = ttk.Scrollbar(result_tab, orient=tk.VERTICAL, command=self.ensemble_result_text.yview)
        self.ensemble_result_text.configure(yscrollcommand=result_scrollbar.set)

        self.ensemble_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 性能对比标签页
        performance_tab = ttk.Frame(result_notebook)
        result_notebook.add(performance_tab, text="性能对比")

        self.performance_text = tk.Text(performance_tab, wrap=tk.WORD, font=('Consolas', 9))
        perf_scrollbar = ttk.Scrollbar(performance_tab, orient=tk.VERTICAL, command=self.performance_text.yview)
        self.performance_text.configure(yscrollcommand=perf_scrollbar.set)

        self.performance_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化显示信息
        self.show_single_ensemble_info()

    # 导航事件处理
    def on_nav_select(self, event):
        """处理导航树选择事件"""
        selection = self.nav_tree.selection()
        if selection:
            item = selection[0]
            tags = self.nav_tree.item(item, "tags")
            if tags:
                tag = tags[0]
                # 根据标签切换到相应的选项卡
                if tag in ["load_data", "preview_data", "validate_data", "preprocess_data"]:
                    self.notebook.select(0)  # 数据管理选项卡
                elif tag == "data_exploration":
                    self.notebook.select(4)  # 数据探索选项卡
                elif tag in ["select_models", "config_params", "start_training", "hyperparameter_tuning"]:
                    self.notebook.select(1)  # 模型训练选项卡
                elif tag in ["single_viz", "compare_viz", "performance_report"]:
                    self.notebook.select(2)  # 结果可视化选项卡
                elif tag in ["single_ensemble", "multi_ensemble", "external_validation"]:
                    self.notebook.select(3)  # 集成学习选项卡
                    # 根据具体选择显示相应功能
                    if tag == "single_ensemble":
                        self.show_single_ensemble_info()
                    elif tag == "multi_ensemble":
                        self.show_multi_ensemble_info()
                    elif tag == "external_validation":
                        self.show_external_validation_info()

    # 数据管理相关方法
    def browse_data_file(self):
        """浏览数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.current_data_path.set(file_path)

    def generate_config(self):
        """生成多数据源配置文件"""
        # 创建配置生成对话框
        config_window = tk.Toplevel(self.root)
        config_window.title("生成多数据源配置")
        config_window.geometry("600x400")
        config_window.transient(self.root)
        config_window.grab_set()

        # 配置映射列表
        ttk.Label(config_window, text="模型-数据源映射配置:").pack(pady=5)

        # 创建配置表格
        columns = ("模型", "数据文件路径")
        config_tree = ttk.Treeview(config_window, columns=columns, show="headings", height=10)

        for col in columns:
            config_tree.heading(col, text=col)
            config_tree.column(col, width=250)

        config_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(config_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        def add_mapping():
            """添加模型-数据映射"""
            add_window = tk.Toplevel(config_window)
            add_window.title("添加映射")
            add_window.geometry("400x150")
            add_window.transient(config_window)
            add_window.grab_set()

            ttk.Label(add_window, text="模型:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
            model_var = tk.StringVar()
            model_combo = ttk.Combobox(add_window, textvariable=model_var, values=MODEL_NAMES, state="readonly")
            model_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)

            ttk.Label(add_window, text="数据文件:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
            data_var = tk.StringVar()
            data_entry = ttk.Entry(add_window, textvariable=data_var, width=30)
            data_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)

            def browse_data():
                file_path = filedialog.askopenfilename(
                    title="选择数据文件",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
                if file_path:
                    data_var.set(file_path)

            ttk.Button(add_window, text="浏览...", command=browse_data).grid(row=1, column=2, padx=5, pady=5)

            def confirm_add():
                if model_var.get() and data_var.get():
                    config_tree.insert("", "end", values=(model_var.get(), data_var.get()))
                    add_window.destroy()
                else:
                    messagebox.showerror("错误", "请填写完整信息")

            ttk.Button(add_window, text="确定", command=confirm_add).grid(row=2, column=1, padx=5, pady=10)
            add_window.columnconfigure(1, weight=1)

        def remove_mapping():
            """删除选中的映射"""
            selected = config_tree.selection()
            if selected:
                config_tree.delete(selected)

        def save_config():
            """保存配置文件"""
            items = config_tree.get_children()
            if not items:
                messagebox.showerror("错误", "请至少添加一个模型-数据映射")
                return

            # 构建配置字典
            model_data_mapping = {}
            for item in items:
                values = config_tree.item(item)['values']
                model_data_mapping[values[0]] = values[1]

            config_data = {
                "model_data_mapping": model_data_mapping,
                "created_time": datetime.now().isoformat(),
                "description": "多数据源集成学习配置文件"
            }

            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if save_path:
                try:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, indent=2, ensure_ascii=False)

                    self.config_path_var.set(save_path)
                    messagebox.showinfo("成功", f"配置文件已保存到:\n{save_path}")
                    config_window.destroy()

                except Exception as e:
                    messagebox.showerror("错误", f"保存配置文件失败:\n{str(e)}")

        ttk.Button(button_frame, text="添加映射", command=add_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="删除映射", command=remove_mapping).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存配置", command=save_config).pack(side=tk.RIGHT, padx=5)

    def load_data_file(self):
        """加载数据文件"""
        data_path = self.current_data_path.get()
        if not data_path:
            messagebox.showwarning("警告", "请先选择数据文件")
            return

        try:
            # 使用数据预处理器加载数据
            import pandas as pd
            from data_preprocessing import load_and_clean_data

            self.status_text.set("正在加载数据...")
            self.root.update()

            # 加载数据
            df, target_col = load_and_clean_data(data_path)

            # 更新数据预览
            self.update_data_preview(df)

            # 更新数据信息
            self.update_data_info(df, target_col)

            self.status_text.set(f"数据加载成功: {df.shape[0]} 行, {df.shape[1]} 列")
            self.log_message(f"成功加载数据文件: {data_path}")

        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")
            self.status_text.set("数据加载失败")
            self.log_message(f"数据加载失败: {e}")

    def update_data_preview(self, df):
        """更新数据预览表格"""
        # 清除现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        # 设置列
        columns = list(df.columns)
        self.data_tree["columns"] = columns
        self.data_tree["show"] = "headings"

        # 设置列标题
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)

        # 插入数据（只显示前100行）
        for i, row in df.head(100).iterrows():
            self.data_tree.insert("", "end", values=list(row))

    def update_data_info(self, df, target_col):
        """更新数据信息显示"""
        info_text = f"数据形状: {df.shape[0]} 行 × {df.shape[1]} 列\n"
        info_text += f"目标列: {target_col}\n"
        info_text += f"特征列数: {df.shape[1] - 1}\n\n"

        # 数据类型信息
        info_text += "数据类型:\n"
        for col, dtype in df.dtypes.items():
            info_text += f"  {col}: {dtype}\n"

        # 缺失值信息
        missing = df.isnull().sum()
        if missing.sum() > 0:
            info_text += "\n缺失值:\n"
            for col, count in missing.items():
                if count > 0:
                    info_text += f"  {col}: {count}\n"
        else:
            info_text += "\n无缺失值\n"

        # 目标变量分布
        if target_col in df.columns:
            target_counts = df[target_col].value_counts()
            info_text += f"\n目标变量分布:\n"
            for value, count in target_counts.items():
                info_text += f"  {value}: {count}\n"

        self.data_info_text.delete(1.0, tk.END)
        self.data_info_text.insert(1.0, info_text)

    # 模型训练相关方法
    def select_all_models(self):
        """全选所有模型"""
        for var in self.model_vars.values():
            var.set(True)

    def deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)

    def recommend_models(self):
        """推荐模型选择"""
        # 简单的推荐逻辑：选择几个常用的高性能模型
        recommended = ["RandomForest", "XGBoost", "LightGBM", "Logistic"]

        # 先取消所有选择
        self.deselect_all_models()

        # 选择推荐的模型
        for model in recommended:
            if model in self.model_vars:
                self.model_vars[model].set(True)

        messagebox.showinfo("推荐", f"已选择推荐模型: {', '.join(recommended)}")

    def update_test_size_label(self, value):
        """更新测试集比例标签"""
        self.test_size_label.config(text=f"{float(value):.2f}")

    def get_selected_models(self):
        """获取选中的模型列表"""
        selected = []
        for model_name, var in self.model_vars.items():
            if var.get():
                selected.append(model_name)
        return selected

    def toggle_tuning_options(self):
        """切换超参数调优选项的启用状态"""
        enabled = self.enable_tuning_var.get()
        # 这里可以添加启用/禁用相关控件的逻辑
        if enabled:
            self.log_message("已启用超参数调优")
        else:
            self.log_message("已禁用超参数调优")

    def get_tuning_config(self):
        """获取超参数调优配置"""
        # 根据策略确定调优模式
        strategy = self.tuning_strategy_var.get()
        quick_mode = strategy == "quick"
        deep_mode = strategy == "deep"
        
        return {
            'enabled': self.enable_tuning_var.get(),
            'n_trials': self.n_trials_var.get(),
            'strategy': strategy,
            'timeout': self.tuning_timeout_var.get() * 60,  # 转换为秒
            'n_jobs': self.tuning_n_jobs_var.get(),
            'metric': self.tuning_metric_var.get(),
            'quick_mode': quick_mode,
            'deep_mode': deep_mode
        }

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 添加到训练日志
        self.training_log.insert(tk.END, log_entry)
        self.training_log.see(tk.END)

        # 添加到右侧日志面板
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 更新界面
        self.root.update()

    # 委托给功能模块的方法
    def load_data(self):
        """加载数据"""
        self.functions.load_data()

    def start_training(self):
        """开始训练"""
        self.functions.start_training()

    def stop_training(self):
        """停止训练"""
        self.functions.stop_training()

    def single_model_visualization(self):
        """单模型可视化"""
        self.functions.single_model_visualization()

    def model_comparison(self):
        """模型比较"""
        self.functions.model_comparison()

    def refresh_chart(self):
        """刷新图表"""
        self.functions.refresh_chart()

    def save_chart(self):
        """保存图表"""
        self.functions.save_chart()

    def generate_detailed_report(self):
        """生成详细报告"""
        self.functions.generate_detailed_report()

    def select_best_model(self):
        """自动选择最佳模型"""
        self.functions.select_best_model_automatically()

    def run_complete_analysis(self):
        """运行完整分析"""
        self.functions.run_complete_analysis()

    # 其他功能方法的简单实现
    def save_project(self):
        """保存项目"""
        file_path = filedialog.asksaveasfilename(
            title="保存项目",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                project_data = {
                    'data_path': self.current_data_path.get(),
                    'selected_models': self.get_selected_models(),
                    'test_size': self.test_size_var.get(),
                    'random_seed': self.random_seed_var.get(),
                    'scaling_method': self.scaling_var.get()
                }
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", f"项目已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存项目失败: {e}")

    def load_project(self):
        """加载项目"""
        file_path = filedialog.askopenfilename(
            title="加载项目",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # 恢复项目设置
                self.current_data_path.set(project_data.get('data_path', ''))
                self.test_size_var.set(project_data.get('test_size', 0.2))
                self.random_seed_var.set(project_data.get('random_seed', 42))
                self.scaling_var.set(project_data.get('scaling_method', 'standard'))

                # 恢复模型选择
                selected_models = project_data.get('selected_models', [])
                for model_name, var in self.model_vars.items():
                    var.set(model_name in selected_models)

                messagebox.showinfo("成功", f"项目已从 {file_path} 加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载项目失败: {e}")

    def export_results(self):
        """导出结果"""
        if not hasattr(self.functions, 'trained_models') or not self.functions.trained_models:
            messagebox.showwarning("警告", "没有可导出的结果，请先训练模型")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出结果",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            try:
                # 创建结果摘要
                results = []
                for model_name in self.functions.trained_models:
                    results.append({
                        'Model': model_name,
                        'Status': 'Trained',
                        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })

                import pandas as pd
                df = pd.DataFrame(results)
                df.to_csv(file_path, index=False)
                messagebox.showinfo("成功", f"结果已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出结果失败: {e}")

    def quit_app(self):
        """退出应用"""
        if messagebox.askokcancel("退出", "确定要退出应用吗？"):
            self.on_closing()

    def clear_cache(self):
        """清除缓存"""
        if messagebox.askokcancel("清除缓存", "确定要清除所有缓存文件吗？"):
            try:
                import shutil
                if CACHE_PATH.exists():
                    shutil.rmtree(CACHE_PATH)
                    CACHE_PATH.mkdir(parents=True, exist_ok=True)
                messagebox.showinfo("成功", "缓存已清除")
            except Exception as e:
                messagebox.showerror("错误", f"清除缓存失败: {e}")

    def reset_settings(self):
        """重置设置"""
        if messagebox.askokcancel("重置设置", "确定要重置所有设置吗？"):
            self.current_data_path.set("")
            self.test_size_var.set(0.2)
            self.random_seed_var.set(42)
            self.scaling_var.set("standard")
            self.deselect_all_models()
            messagebox.showinfo("成功", "设置已重置")

    def check_results(self):
        """检查任务结果（定时器）"""
        if not self.is_running:
            return
        try:
            # 这里可以添加定期检查任务状态的逻辑
            timer_id = self.root.after(1000, self.check_results)  # 每秒检查一次
            self.timer_ids.append(timer_id)
        except tk.TclError:
            # GUI已关闭，停止定时器
            self.is_running = False

    def on_closing(self):
        """处理窗口关闭事件"""
        self.is_running = False

        # 取消所有定时器
        for timer_id in self.timer_ids:
            try:
                self.root.after_cancel(timer_id)
            except tk.TclError:
                pass

        # 停止所有正在运行的训练线程
        if hasattr(self.functions, 'is_training'):
            self.functions.is_training = False

        # 关闭窗口
        self.root.quit()
        self.root.destroy()

    # 占位方法（暂未实现的功能）
    def train_models(self): self.start_training()
    def visualize_results(self): self.single_model_visualization()
    def compare_models(self): self.model_comparison()
    def hyperparameter_tuning(self): self.start_hyperparameter_tuning()
    def start_hyperparameter_tuning(self): self.functions.start_hyperparameter_tuning()
    def show_tuning_results(self): self.functions.show_tuning_results()
    def ensemble_learning(self):
        """开始集成学习"""
        if not self.current_data_path.get():
            messagebox.showerror("错误", "请先选择数据文件")
            return

        # 获取选中的模型
        selected_models = [model for model, var in self.model_vars.items() if var.get()]
        if len(selected_models) < 2:
            messagebox.showerror("错误", "集成学习需要至少选择2个模型")
            return

        # 获取选中的集成方法
        selected_methods = [method for method, var in self.ensemble_methods.items() if var.get()]
        if not selected_methods:
            messagebox.showerror("错误", "请至少选择一种集成方法")
            return

        # 开始进度显示
        self.ensemble_progress.start()
        self.ensemble_status_var.set("正在执行集成学习...")

        def run_ensemble():
            try:
                self.ensemble_result_text.delete(1.0, tk.END)
                self.ensemble_result_text.insert(tk.END, "正在加载数据...\n")
                self.root.update()

                # 加载数据
                X_train, X_test, y_train, y_test = load_and_preprocess_data(self.current_data_path.get())

                self.ensemble_result_text.insert(tk.END, f"数据加载完成: 训练集{X_train.shape}, 测试集{X_test.shape}\n")
                self.ensemble_result_text.insert(tk.END, f"开始集成学习，模型: {', '.join(selected_models)}\n")
                self.ensemble_result_text.insert(tk.END, f"集成方法: {', '.join(selected_methods)}\n\n")
                self.root.update()

                # 运行集成学习
                ensemble_results = run_ensemble_pipeline(
                    X_train=X_train,
                    y_train=y_train,
                    X_test=X_test,
                    y_test=y_test,
                    model_names=selected_models,
                    ensemble_methods=selected_methods,
                    save_results=True,
                    output_dir=OUTPUT_PATH / 'ensemble',
                    enable_shap=True
                )

                if ensemble_results:
                    self.ensemble_result_text.insert(tk.END, "集成学习完成！\n\n")
                    self.ensemble_result_text.insert(tk.END, "=== 集成结果 ===\n")

                    # 显示结果
                    performance_data = []
                    for name, result in ensemble_results.items():
                        metrics = result['metrics']
                        self.ensemble_result_text.insert(tk.END, f"\n{name}:\n")
                        self.ensemble_result_text.insert(tk.END, f"  准确率: {metrics['accuracy']:.4f}\n")
                        self.ensemble_result_text.insert(tk.END, f"  精确率: {metrics['precision']:.4f}\n")
                        self.ensemble_result_text.insert(tk.END, f"  召回率: {metrics['recall']:.4f}\n")
                        self.ensemble_result_text.insert(tk.END, f"  F1分数: {metrics['f1_score']:.4f}\n")
                        self.ensemble_result_text.insert(tk.END, f"  AUC: {metrics['auc']:.4f}\n")

                        # 收集性能数据用于对比显示
                        performance_data.append((name, metrics))

                    # 找出最佳模型
                    best_model = max(ensemble_results.keys(),
                                   key=lambda x: ensemble_results[x]['metrics']['f1_score'])
                    best_metrics = ensemble_results[best_model]['metrics']

                    self.ensemble_result_text.insert(tk.END, f"\n🏆 最佳集成模型: {best_model}\n")
                    self.ensemble_result_text.insert(tk.END, f"   F1分数: {best_metrics['f1_score']:.4f}\n")
                    self.ensemble_result_text.insert(tk.END, f"   准确率: {best_metrics['accuracy']:.4f}\n")

                    # 更新性能对比显示
                    self.update_performance_comparison(performance_data)

                    # 保存结果供可视化使用
                    self.current_ensemble_results = ensemble_results
                    self.current_ensemble_data = (X_train, y_train, X_test, y_test)

                    # 停止进度显示
                    self.ensemble_progress.stop()
                    self.ensemble_status_var.set("集成学习完成")

                    messagebox.showinfo("成功", "集成学习完成！可以查看可视化结果。")
                else:
                    self.ensemble_result_text.insert(tk.END, "集成学习失败！\n")
                    self.ensemble_progress.stop()
                    self.ensemble_status_var.set("集成学习失败")
                    messagebox.showerror("错误", "集成学习失败")

            except Exception as e:
                error_msg = f"集成学习过程中出错: {str(e)}"
                self.ensemble_result_text.insert(tk.END, f"\n错误: {error_msg}\n")
                self.ensemble_progress.stop()
                self.ensemble_status_var.set("执行出错")
                messagebox.showerror("错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_ensemble, daemon=True).start()

    def update_performance_comparison(self, performance_data):
        """更新性能对比显示"""
        self.performance_text.delete(1.0, tk.END)

        if not performance_data:
            self.performance_text.insert(tk.END, "暂无性能数据\n")
            return

        # 表头
        self.performance_text.insert(tk.END, "=" * 80 + "\n")
        self.performance_text.insert(tk.END, "集成学习性能对比表\n")
        self.performance_text.insert(tk.END, "=" * 80 + "\n\n")

        # 表格标题
        header = f"{'模型名称':<15} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'AUC':<8} {'MCC':<8}\n"
        self.performance_text.insert(tk.END, header)
        self.performance_text.insert(tk.END, "-" * 80 + "\n")

        # 按F1分数排序
        sorted_data = sorted(performance_data, key=lambda x: x[1]['f1_score'], reverse=True)

        # 显示每个模型的性能
        for i, (name, metrics) in enumerate(sorted_data):
            rank_symbol = "🏆" if i == 0 else f"{i+1:2d}"
            row = f"{rank_symbol} {name:<12} {metrics['accuracy']:<8.4f} {metrics['precision']:<8.4f} " \
                  f"{metrics['recall']:<8.4f} {metrics['f1_score']:<8.4f} {metrics['auc']:<8.4f} " \
                  f"{metrics.get('mcc', 0):<8.4f}\n"
            self.performance_text.insert(tk.END, row)

        # 统计信息
        self.performance_text.insert(tk.END, "\n" + "=" * 80 + "\n")
        self.performance_text.insert(tk.END, "统计信息:\n")

        f1_scores = [metrics['f1_score'] for _, metrics in performance_data]
        accuracies = [metrics['accuracy'] for _, metrics in performance_data]

        self.performance_text.insert(tk.END, f"• 平均F1分数: {sum(f1_scores)/len(f1_scores):.4f}\n")
        self.performance_text.insert(tk.END, f"• 平均准确率: {sum(accuracies)/len(accuracies):.4f}\n")
        self.performance_text.insert(tk.END, f"• 最佳F1分数: {max(f1_scores):.4f}\n")
        self.performance_text.insert(tk.END, f"• 最佳准确率: {max(accuracies):.4f}\n")
        self.performance_text.insert(tk.END, f"• 性能提升: {((max(f1_scores) - min(f1_scores))/min(f1_scores)*100):.2f}%\n")

    def start_ensemble(self):
        self.ensemble_learning()

    def ensemble_visualization(self):
        """集成学习可视化 - 使用与命令行版本相同的SHAP分析"""
        if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
            messagebox.showwarning("Warning", "Please run ensemble learning first")
            return

        def run_enhanced_visualization():
            try:
                self.ensemble_result_text.insert(tk.END, "\nGenerating enhanced visualization with SHAP analysis...\n")
                self.root.update()

                # 使用与命令行版本相同的增强SHAP可视化
                try:
                    import sys
                    from pathlib import Path
                    sys.path.append(str(Path(__file__).parent / 'code'))

                    from enhanced_shap_visualization import create_complete_shap_analysis
                    from safe_visualization import safe_ensemble_performance_plot, safe_create_summary_report

                    output_dir = OUTPUT_PATH / 'ensemble' / 'visualizations'
                    output_dir.mkdir(parents=True, exist_ok=True)

                    # 生成基础性能对比图
                    plot_success = safe_ensemble_performance_plot(
                        self.current_ensemble_results,
                        output_dir / f"ensemble_performance.{SAVE_FORMAT}"
                    )

                    # 生成总结报告
                    report_success = safe_create_summary_report(
                        self.current_ensemble_results,
                        output_dir
                    )

                    if plot_success:
                        self.ensemble_result_text.insert(tk.END, "✅ Performance chart generated successfully!\n")
                    if report_success:
                        self.ensemble_result_text.insert(tk.END, "✅ Summary report generated successfully!\n")

                    # 为每个集成模型生成SHAP分析（与命令行版本一致）
                    if hasattr(self, 'current_data_path') and self.current_data_path.get():
                        self.ensemble_result_text.insert(tk.END, "\nGenerating SHAP analysis for ensemble models...\n")
                        self.root.update()

                        # 加载测试数据
                        import pandas as pd
                        from sklearn.model_selection import train_test_split

                        data = pd.read_csv(self.current_data_path.get())
                        X = data.drop(data.columns[-1], axis=1)  # 假设最后一列是目标变量
                        y = data.iloc[:, -1]

                        # 分割数据
                        _, X_test, _, _ = train_test_split(X, y, test_size=0.2, random_state=42)

                        # 获取真实的特征名称
                        real_feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]

                        shap_success_count = 0
                        for name, result in self.current_ensemble_results.items():
                            if 'model' in result:
                                try:
                                    self.ensemble_result_text.insert(tk.END, f"  Generating SHAP for {name}...\n")
                                    self.root.update()

                                    # 创建模型专用输出目录
                                    model_output_dir = output_dir / 'shap_analysis' / name
                                    model_output_dir.mkdir(parents=True, exist_ok=True)

                                    # 使用与命令行版本完全相同的SHAP分析
                                    from gui_functions import GUIFunctions
                                    gui_func = GUIFunctions(self)
                                    shap_success = gui_func.create_command_line_shap_plots(
                                        model=result['model'],
                                        X_test=X_test,
                                        model_name=name,
                                        output_dir=model_output_dir
                                    )

                                    if shap_success:
                                        shap_success_count += 1
                                        self.ensemble_result_text.insert(tk.END, f"    ✅ {name} SHAP analysis completed\n")

                                        # 记录生成的文件
                                        self.ensemble_result_text.insert(tk.END, f"      Generated: summary, decision, waterfall plots\n")
                                    else:
                                        self.ensemble_result_text.insert(tk.END, f"    ⚠️ {name} SHAP analysis failed\n")

                                except Exception as e:
                                    self.ensemble_result_text.insert(tk.END, f"    ❌ {name} SHAP analysis failed: {e}\n")

                        if shap_success_count > 0:
                            self.ensemble_result_text.insert(tk.END, f"\n✅ SHAP analysis completed for {shap_success_count} models\n")
                        else:
                            self.ensemble_result_text.insert(tk.END, "\n⚠️ No SHAP analysis was generated\n")

                    self.ensemble_result_text.insert(tk.END, f"\nAll charts saved to: {output_dir}\n")
                    messagebox.showinfo("Success", f"Enhanced visualization with SHAP analysis completed!\nCharts saved to:\n{output_dir}")

                except ImportError as e:
                    self.ensemble_result_text.insert(tk.END, f"\nWarning: Enhanced SHAP visualization not available: {e}\n")
                    self.ensemble_result_text.insert(tk.END, "Falling back to basic visualization...\n")

                    # 回退到基础可视化
                    sys.path.append(str(Path(__file__).parent / 'code'))
                    from safe_visualization import safe_ensemble_performance_plot, safe_create_summary_report

                    output_dir = OUTPUT_PATH / 'ensemble' / 'visualizations'
                    output_dir.mkdir(parents=True, exist_ok=True)

                    plot_success = safe_ensemble_performance_plot(
                        self.current_ensemble_results,
                        output_dir / f"ensemble_performance.{SAVE_FORMAT}"
                    )
                    report_success = safe_create_summary_report(
                        self.current_ensemble_results,
                        output_dir
                    )

                    if plot_success and report_success:
                        messagebox.showinfo("Success", f"Basic visualization completed!\nCharts saved to:\n{output_dir}")

            except Exception as e:
                error_msg = f"Visualization failed: {str(e)}"
                self.ensemble_result_text.insert(tk.END, f"\nError: {error_msg}\n")
                messagebox.showerror("Error", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_enhanced_visualization, daemon=True).start()

    def external_validation(self):
        """外部验证功能"""
        if not hasattr(self, 'current_ensemble_results') or not self.current_ensemble_results:
            messagebox.showwarning("警告", "请先运行集成学习")
            return

        # 选择外部验证数据文件
        external_data_path = filedialog.askopenfilename(
            title="选择外部验证数据文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if not external_data_path:
            return

        def run_external_validation():
            try:
                self.ensemble_result_text.insert(tk.END, f"\n正在进行外部验证...\n")
                self.ensemble_result_text.insert(tk.END, f"外部数据: {external_data_path}\n")
                self.root.update()

                # 运行外部验证
                validation_results = run_external_validation(
                    external_data_path=external_data_path,
                    output_dir=OUTPUT_PATH / 'external_validation'
                )

                if validation_results:
                    self.ensemble_result_text.insert(tk.END, "外部验证完成！\n")
                    self.ensemble_result_text.insert(tk.END, f"验证结果保存到: {OUTPUT_PATH / 'external_validation'}\n")
                    messagebox.showinfo("成功", "外部验证完成！")
                else:
                    self.ensemble_result_text.insert(tk.END, "外部验证失败！\n")
                    messagebox.showerror("错误", "外部验证失败")

            except Exception as e:
                error_msg = f"外部验证过程中出错: {str(e)}"
                self.ensemble_result_text.insert(tk.END, f"\n错误: {error_msg}\n")
                messagebox.showerror("错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_external_validation, daemon=True).start()

    def show_single_ensemble_info(self):
        """显示单数据源集成学习信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 单数据源集成学习 ===

使用说明:
1. 确保已选择数据文件
2. 选择至少2个要集成的模型
3. 选择集成方法（投票法、装袋法、提升法、堆叠法）
4. 点击"开始集成"按钮开始训练
5. 训练完成后可查看可视化结果

支持的集成方法:
• Voting (投票法): 硬投票和软投票
• Bagging (装袋法): 基于Bootstrap采样
• Boosting (提升法): AdaBoost算法
• Stacking (堆叠法): 使用元分类器

特点:
- 自动模型训练和集成
- 性能指标对比
- SHAP可解释性分析
- 可视化图表生成
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def show_multi_ensemble_info(self):
        """显示多数据源集成学习信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 多数据源集成学习 ===

使用说明:
1. 点击"生成配置"创建模型-数据源映射
2. 或选择已有的配置文件
3. 选择集成方法和数据策略
4. 配置特征选择参数
5. 点击"开始多数据源集成"

数据策略:
• Unified: 使用统一测试数据评估
• Original: 每个模型使用对应测试数据
• Combined: 合并所有数据源

特征选择方法:
• Combined: 组合特征重要性
• Union: 特征并集
• Intersection: 特征交集
• Weighted: 加权特征选择
• Meta_model: 元模型特征选择

注意事项:
- 不同数据源应具有相同的目标变量
- 特征名称可以不同，系统会自动处理
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def show_external_validation_info(self):
        """显示外部验证信息"""
        self.ensemble_result_text.delete(1.0, tk.END)
        info_text = """=== 外部验证 ===

使用说明:
1. 先完成集成学习训练
2. 点击"外部验证"按钮
3. 选择外部验证数据文件
4. 系统将使用训练好的模型进行验证

验证内容:
• 模型在外部数据上的性能
• 性能指标计算和对比
• 预测结果分析
• 可视化图表生成

数据要求:
- CSV格式文件
- 包含与训练数据相同的特征
- 可以有不同的样本数量
- 目标变量名称应一致

输出结果:
- 验证性能报告
- 预测结果文件
- 可视化图表
- 详细分析报告
"""
        self.ensemble_result_text.insert(tk.END, info_text)

    def start_multi_data_ensemble(self):
        """开始多数据源集成学习"""
        config_path = self.config_path_var.get()
        if not config_path:
            messagebox.showerror("错误", "请先选择或生成配置文件")
            return

        # 获取选中的集成方法
        selected_methods = [method for method, var in self.ensemble_methods.items() if var.get()]
        if not selected_methods:
            messagebox.showerror("错误", "请至少选择一种集成方法")
            return

        # 获取选中的数据策略
        selected_strategies = [strategy for strategy, var in self.data_strategies.items() if var.get()]
        if not selected_strategies:
            messagebox.showerror("错误", "请至少选择一种数据策略")
            return

        def run_multi_ensemble():
            try:
                self.ensemble_result_text.delete(1.0, tk.END)
                self.ensemble_result_text.insert(tk.END, "正在加载配置文件...\n")
                self.root.update()

                # 读取配置文件
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    model_data_mapping = config_data.get('model_data_mapping', {})

                if not model_data_mapping:
                    raise ValueError("配置文件中未找到有效的模型-数据映射")

                self.ensemble_result_text.insert(tk.END, f"配置加载完成，包含 {len(model_data_mapping)} 个映射\n")
                self.ensemble_result_text.insert(tk.END, f"集成方法: {', '.join(selected_methods)}\n")
                self.ensemble_result_text.insert(tk.END, f"数据策略: {', '.join(selected_strategies)}\n\n")
                self.root.update()

                # 运行多数据源集成学习
                results = run_multi_data_ensemble_pipeline(
                    model_data_mapping=model_data_mapping,
                    ensemble_methods=selected_methods,
                    ensemble_data_strategies=selected_strategies,
                    target_data_path=None,
                    feature_names=None,
                    enable_shap=True,
                    feature_selection=self.feature_selection_var.get(),
                    feature_selection_method=self.feature_method_var.get(),
                    k=self.feature_k_var.get() if self.feature_k_var.get() > 0 else None
                )

                if results:
                    self.ensemble_result_text.insert(tk.END, "多数据源集成学习完成！\n\n")
                    self.ensemble_result_text.insert(tk.END, "=== 集成结果 ===\n")

                    # 显示结果
                    for strategy, method, ensemble_model, metrics in results:
                        self.ensemble_result_text.insert(tk.END, f"\n{strategy}_{method}:\n")
                        if isinstance(metrics, dict):
                            for metric_name, value in metrics.items():
                                if isinstance(value, (int, float)):
                                    self.ensemble_result_text.insert(tk.END, f"  {metric_name}: {value:.4f}\n")

                    self.ensemble_result_text.insert(tk.END, f"\n结果已保存到: {OUTPUT_PATH / 'ensemble'}\n")
                    messagebox.showinfo("成功", "多数据源集成学习完成！")
                else:
                    self.ensemble_result_text.insert(tk.END, "多数据源集成学习失败！\n")
                    messagebox.showerror("错误", "多数据源集成学习失败")

            except Exception as e:
                error_msg = f"多数据源集成学习过程中出错: {str(e)}"
                self.ensemble_result_text.insert(tk.END, f"\n错误: {error_msg}\n")
                messagebox.showerror("错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_multi_ensemble, daemon=True).start()

    def smart_model_selection(self):
        """智能模型选择功能"""
        if not self.current_data_path.get():
            messagebox.showerror("错误", "请先选择数据文件")
            return

        def run_smart_selection():
            try:
                self.ensemble_result_text.delete(1.0, tk.END)
                self.ensemble_result_text.insert(tk.END, "正在进行智能模型选择分析...\n")
                self.root.update()

                # 导入增强的集成选择器
                sys.path.append(str(Path(__file__).parent / 'code'))
                from enhanced_ensemble_selector import EnhancedEnsembleSelector
                from data_preprocessing import load_and_preprocess_data

                # 加载数据
                X_train, X_test, y_train, y_test = load_and_preprocess_data(self.current_data_path.get())

                # 创建选择器
                selector = EnhancedEnsembleSelector(
                    correlation_threshold=self.correlation_threshold_var.get(),
                    min_performance_threshold=self.min_performance_var.get()
                )

                self.ensemble_result_text.insert(tk.END, "正在评估基模型性能...\n")
                self.root.update()

                # 评估所有可用模型
                available_models = list(MODEL_TRAINERS.keys())
                model_results = selector.evaluate_base_models(
                    X_train, y_train, X_test, y_test, available_models
                )

                self.ensemble_result_text.insert(tk.END, f"完成 {len(model_results)} 个模型的评估\n")
                self.root.update()

                # 选择最优组合
                selection_result = selector.select_optimal_ensemble(
                    target_size=self.target_size_var.get(),
                    strategy=self.selection_strategy_var.get()
                )

                if selection_result:
                    # 显示选择结果
                    report = selector.generate_selection_report(selection_result)
                    self.ensemble_result_text.insert(tk.END, "\n" + report + "\n")

                    # 如果使用了量化评估，显示量化多样性报告
                    if (self.selection_strategy_var.get() == 'quantified' and
                        hasattr(selector, 'quantified_diversity_results') and
                        selector.quantified_diversity_results):

                        self.ensemble_result_text.insert(tk.END, "\n" + "="*60 + "\n")
                        self.ensemble_result_text.insert(tk.END, "量化多样性分析报告\n")
                        self.ensemble_result_text.insert(tk.END, "="*60 + "\n")

                        # 获取预测结果
                        predictions = {name: selector.model_results[name]['predictions']
                                     for name in selector.model_results.keys()}

                        # 生成量化多样性报告
                        quantified_report = selector.quantified_evaluator.generate_comprehensive_diversity_report(
                            y_test, predictions, selector.performance_scores
                        )

                        self.ensemble_result_text.insert(tk.END, quantified_report + "\n")

                    # 自动更新模型选择
                    selected_models = selection_result['selected_models']

                    # 清除当前选择
                    for model, var in self.model_vars.items():
                        var.set(False)

                    # 设置推荐的模型
                    for model in selected_models:
                        if model in self.model_vars:
                            self.model_vars[model].set(True)

                    self.ensemble_result_text.insert(tk.END, f"\n✅ 已自动选择推荐模型: {', '.join(selected_models)}\n")

                    # 计算最优权重
                    optimal_weights = selector.calculate_optimal_weights(
                        selected_models, method='performance_diversity'
                    )

                    self.ensemble_result_text.insert(tk.END, "\n推荐融合权重:\n")
                    for model, weight in optimal_weights.items():
                        self.ensemble_result_text.insert(tk.END, f"  {model}: {weight:.4f}\n")

                    # 保存选择结果供后续使用
                    self.current_selection_result = selection_result
                    self.current_optimal_weights = optimal_weights

                    messagebox.showinfo("成功", "智能模型选择完成！已自动选择推荐的模型组合。")
                else:
                    self.ensemble_result_text.insert(tk.END, "\n❌ 智能模型选择失败\n")
                    messagebox.showerror("错误", "智能模型选择失败")

            except Exception as e:
                error_msg = f"智能模型选择过程中出错: {str(e)}"
                self.ensemble_result_text.insert(tk.END, f"\n错误: {error_msg}\n")
                messagebox.showerror("错误", error_msg)

        # 在后台线程中运行
        threading.Thread(target=run_smart_selection, daemon=True).start()

    def view_ensemble_results(self):
        """查看集成学习结果"""
        ensemble_dir = OUTPUT_PATH / 'ensemble'
        if not ensemble_dir.exists():
            messagebox.showinfo("提示", "尚未运行集成学习，没有结果可查看")
            return

        # 查找结果文件
        result_files = list(ensemble_dir.glob('*results*.joblib'))
        if not result_files:
            messagebox.showinfo("提示", "未找到集成学习结果文件")
            return

        # 显示最新的结果文件
        latest_file = max(result_files, key=lambda x: x.stat().st_mtime)

        self.ensemble_result_text.delete(1.0, tk.END)
        self.ensemble_result_text.insert(tk.END, f"=== 集成学习结果 ===\n")
        self.ensemble_result_text.insert(tk.END, f"结果文件: {latest_file.name}\n")
        self.ensemble_result_text.insert(tk.END, f"修改时间: {datetime.fromtimestamp(latest_file.stat().st_mtime)}\n\n")

        try:
            from joblib import load
            results = load(latest_file)

            if isinstance(results, dict) and 'ensemble_results' in results:
                ensemble_results = results['ensemble_results']
                self.ensemble_result_text.insert(tk.END, "=== 性能指标 ===\n")

                for name, result in ensemble_results.items():
                    if 'metrics' in result:
                        metrics = result['metrics']
                        self.ensemble_result_text.insert(tk.END, f"\n{name}:\n")
                        for metric_name, value in metrics.items():
                            if isinstance(value, (int, float)):
                                self.ensemble_result_text.insert(tk.END, f"  {metric_name}: {value:.4f}\n")

                if 'best_model' in results:
                    best_model = results['best_model']
                    self.ensemble_result_text.insert(tk.END, f"\n🏆 最佳模型: {best_model}\n")

        except Exception as e:
            self.ensemble_result_text.insert(tk.END, f"读取结果文件失败: {str(e)}\n")

    def open_output_directory(self):
        """打开输出目录"""
        import subprocess
        import platform

        output_dir = OUTPUT_PATH

        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(output_dir)], check=True)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(output_dir)], check=True)
            else:  # Linux
                subprocess.run(["xdg-open", str(output_dir)], check=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开输出目录: {str(e)}")
            # 备选方案：显示路径
            messagebox.showinfo("输出目录", f"输出目录路径:\n{output_dir}")
    def generate_report(self):
        """生成模型性能比较报告"""
        self.functions.generate_performance_report()

    def generate_performance_report(self):
        """生成性能报告的包装函数"""
        self.generate_report()
    def browse_config_file(self): messagebox.showinfo("提示", "配置文件浏览功能开发中...")
    def generate_config(self): messagebox.showinfo("提示", "配置生成功能开发中...")
    def toggle_log_panel(self): messagebox.showinfo("提示", "日志面板切换功能开发中...")
    def toggle_fullscreen(self): messagebox.showinfo("提示", "全屏切换功能开发中...")
    def data_quality_check(self): messagebox.showinfo("提示", "数据质量检查功能开发中...")
    def model_selection_advisor(self): messagebox.showinfo("提示", "模型选择建议功能开发中...")
    def config_generator(self): messagebox.showinfo("提示", "配置生成器功能开发中...")
    def show_help(self): messagebox.showinfo("帮助", "多模型集成机器学习平台\n\n使用说明:\n1. 加载数据文件\n2. 选择要训练的模型\n3. 配置训练参数\n4. 开始训练\n5. 查看结果和可视化")
    def show_about(self): messagebox.showinfo("关于", "多模型集成机器学习平台 v1.0\n\n基于Python和Tkinter开发\n支持多种机器学习算法")

    def run(self):
        """启动GUI应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MLPlatformGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"启动GUI失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
