#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试绘图样式改进
"""

import numpy as np
import matplotlib.pyplot as plt
import os
from pathlib import Path

# 创建输出目录
output_dir = Path('test_output')
output_dir.mkdir(exist_ok=True)

# 设置绘图样式
plt.style.use('seaborn-v0_8-whitegrid')

# 设置默认字体大小和风格
PLOT_CONFIG = {
    'figsize': (10, 7),  # 稍微宽一些，更美观
    'dpi': 150,          # 默认分辨率
    'fontsize': {
        'title': 16,       # 标题字体大小
        'subtitle': 14,    # 副标题字体大小
        'label': 12,       # 轴标签字体大小
        'tick': 10,        # 刻度标签字体大小
        'legend': 10,      # 图例字体大小
        'annotation': 10   # 注释字体大小
    },
    'colors': {
        'primary': '#1f77b4',   # 蓝色
        'secondary': '#ff7f0e', # 橙色
        'tertiary': '#2ca02c',  # 绿色
        'highlight': '#d62728', # 红色
        'grid': '#cccccc',      # 浅灰色
        'background': '#f8f9fa' # 非常浅的灰色背景
    },
    'marker_size': 8,
    'line_width': 2,
    'grid_alpha': 0.3
}

# 测试ROC曲线
def test_roc_curve():
    # 模拟数据
    fpr = np.array([0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])
    tpr = np.array([0, 0.4, 0.65, 0.8, 0.85, 0.9, 0.93, 0.95, 0.97, 0.99, 1.0])
    roc_auc = 0.85
    
    # 创建图形
    fig, ax = plt.subplots(figsize=PLOT_CONFIG['figsize'])
    
    # 设置图形背景色
    fig.patch.set_facecolor(PLOT_CONFIG['colors']['background'])
    ax.set_facecolor(PLOT_CONFIG['colors']['background'])
    
    # 绘制ROC曲线
    ax.plot(fpr, tpr, color=PLOT_CONFIG['colors']['primary'], 
            linewidth=PLOT_CONFIG['line_width'], 
            label=f"RandomForest (AUC = {roc_auc:.3f})")
    
    # 添加对角线参考
    ax.plot([0, 1], [0, 1], color='gray', linestyle='--', 
            linewidth=1.5, alpha=0.7, label="Random")
    
    # 找到约登指数最大点
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = optimal_idx / len(tpr)
    
    # 标记最优点
    ax.scatter(fpr[optimal_idx], tpr[optimal_idx], 
               s=100, color=PLOT_CONFIG['colors']['highlight'], alpha=0.8, 
               label=f"Optimal (t={optimal_threshold:.3f})")
    
    # 设置轴范围和标签
    ax.set_xlim([0.0, 1.0])
    ax.set_ylim([0.0, 1.05])
    
    # 应用样式
    title = "ROC Curve"
    subtitle = "(Balanced Data, n=40)"
    ax.set_title(f"{title} - RandomForest\n{subtitle}", 
                 fontsize=PLOT_CONFIG['fontsize']['title'])
    ax.set_xlabel("False Positive Rate (1 - Specificity)", 
                  fontsize=PLOT_CONFIG['fontsize']['label'])
    ax.set_ylabel("True Positive Rate (Sensitivity)", 
                  fontsize=PLOT_CONFIG['fontsize']['label'])
    
    # 设置网格
    ax.grid(True, alpha=PLOT_CONFIG['grid_alpha'], linestyle='--', 
            color=PLOT_CONFIG['colors']['grid'])
    
    # 去除顶部和右侧边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=PLOT_CONFIG['fontsize']['tick'])
    
    # 添加图例
    ax.legend(loc="lower right", fontsize=PLOT_CONFIG['fontsize']['legend'])
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_dir / 'test_roc_curve.png', dpi=PLOT_CONFIG['dpi'], bbox_inches='tight')
    print(f"ROC曲线图已保存到: {output_dir / 'test_roc_curve.png'}")
    
    # 关闭图形
    plt.close(fig)

# 测试学习曲线
def test_learning_curve():
    # 模拟数据
    train_sizes = np.linspace(0.1, 1.0, 10) * 128
    train_mean = np.array([1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0])
    train_std = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    test_mean = np.array([0.95, 0.97, 0.96, 0.96, 0.97, 0.98, 0.97, 0.98, 0.98, 0.98])
    test_std = np.array([0.03, 0.02, 0.03, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.01])
    
    # 创建图形
    fig, ax = plt.subplots(figsize=PLOT_CONFIG['figsize'])
    
    # 设置图形背景色
    fig.patch.set_facecolor(PLOT_CONFIG['colors']['background'])
    ax.set_facecolor(PLOT_CONFIG['colors']['background'])
    
    # 填充标准差区域
    ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                   alpha=0.1, color=PLOT_CONFIG['colors']['primary'])
    ax.fill_between(train_sizes, test_mean - test_std, test_mean + test_std, 
                   alpha=0.1, color=PLOT_CONFIG['colors']['secondary'])
    
    # 绘制均值线
    ax.plot(train_sizes, train_mean, 'o-', 
           color=PLOT_CONFIG['colors']['primary'], 
           markersize=PLOT_CONFIG['marker_size'], 
           linewidth=PLOT_CONFIG['line_width'], 
           label="Training Set")
    ax.plot(train_sizes, test_mean, 'o-', 
           color=PLOT_CONFIG['colors']['secondary'], 
           markersize=PLOT_CONFIG['marker_size'], 
           linewidth=PLOT_CONFIG['line_width'], 
           label="Validation Set")
    
    # 设置Y轴范围以便更好地展示差异
    ax.set_ylim([0.8, 1.01])
    
    # 标记性能指标
    final_train = train_mean[-1]
    final_test = test_mean[-1]
    gap = final_train - final_test
    
    # 添加性能注释框
    perf_text = f"Training AUC: {final_train:.3f}\nValidation AUC: {final_test:.3f}\nGap: {gap:.3f}"
    
    # 根据差距评估模型
    if gap < 0.05:
        evaluation = "Good model performance"
        box_color = "lightgreen"
    elif gap < 0.1:
        evaluation = "Slight overfitting"
        box_color = "khaki"
    else:
        evaluation = "Significant overfitting"
        box_color = "lightcoral"
    
    # 添加评估注释
    bbox_props = dict(boxstyle="round,pad=0.5", facecolor=box_color, alpha=0.7)
    ax.text(0.05, 0.95, perf_text + "\n\n" + evaluation, transform=ax.transAxes, 
           fontsize=PLOT_CONFIG['fontsize']['annotation'], verticalalignment='top', bbox=bbox_props)
    
    # 应用样式
    ax.set_title(f"Learning Curve - RandomForest", 
                 fontsize=PLOT_CONFIG['fontsize']['title'])
    ax.set_xlabel("Training Examples", fontsize=PLOT_CONFIG['fontsize']['label'])
    ax.set_ylabel("AUC Score", fontsize=PLOT_CONFIG['fontsize']['label'])
    
    # 设置网格
    ax.grid(True, alpha=PLOT_CONFIG['grid_alpha'], linestyle='--', 
            color=PLOT_CONFIG['colors']['grid'])
    
    # 去除顶部和右侧边框
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # 设置刻度标签字体大小
    ax.tick_params(axis='both', labelsize=PLOT_CONFIG['fontsize']['tick'])
    
    # 添加图例
    ax.legend(loc="lower right", fontsize=PLOT_CONFIG['fontsize']['legend'])
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_dir / 'test_learning_curve.png', dpi=PLOT_CONFIG['dpi'], bbox_inches='tight')
    print(f"学习曲线图已保存到: {output_dir / 'test_learning_curve.png'}")
    
    # 关闭图形
    plt.close(fig)

if __name__ == "__main__":
    print("测试绘图样式改进...")
    test_roc_curve()
    test_learning_curve()
    print("测试完成！") 