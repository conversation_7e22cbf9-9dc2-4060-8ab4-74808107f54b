#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数调优模块
使用Optuna优化各种机器学习模型的超参数
支持GPU加速和自动记录最佳性能
"""

import optuna
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
import warnings
import time
import matplotlib.pyplot as plt
from pathlib import Path
import os

# 过滤一些常见的警告以减少输出噪音
warnings.filterwarnings("ignore", category=UserWarning, module="xgboost")
warnings.filterwarnings("ignore", category=FutureWarning, module="sklearn")

# 尝试导入配置模块
try:
    from config import (
        GPU_CONFIG, OUTPUT_PATH, RANDOM_SEED,
        get_optimized_gpu_config, detect_gpu_availability,
        get_dataset_type, get_optuna_param_space,
        STANDARD_TUNING_CONFIG, QUICK_TUNING_CONFIG, DEEP_TUNING_CONFIG
    )
    # 获取优化的GPU配置
    OPTIMIZED_GPU_CONFIG = get_optimized_gpu_config()
    USE_GPU = OPTIMIZED_GPU_CONFIG.get('use_gpu', False)
    GPU_IDS = OPTIMIZED_GPU_CONFIG.get('gpu_ids', [0])
    GPU_PRECISION = OPTIMIZED_GPU_CONFIG.get('precision', 'float32')

    # 检测GPU可用性
    GPU_INFO = detect_gpu_availability()

except ImportError:
    # 默认设置
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    OUTPUT_PATH.mkdir(parents=True, exist_ok=True)
    USE_GPU = False
    GPU_IDS = [0]
    GPU_PRECISION = 'float32'
    GPU_INFO = {'xgboost_gpu': False, 'lightgbm_gpu': False, 'catboost_gpu': False}
    RANDOM_SEED = 42
    
    # 默认调优配置
    STANDARD_TUNING_CONFIG = {'n_trials': 50, 'cv_folds': 5}
    QUICK_TUNING_CONFIG = {'n_trials': 10, 'cv_folds': 3}
    DEEP_TUNING_CONFIG = {'n_trials': 100, 'cv_folds': 10}
    
    def get_dataset_type(X):
        """简单的数据集类型判断"""
        n_samples, n_features = X.shape
        if n_samples < 1000:
            return 'small'
        elif n_samples > 10000:
            return 'large'
        elif n_features > 100:
            return 'high_dim'
        return 'default'
    
    def get_optuna_param_space(model_name, dataset_type='default', custom_params=None):
        """简单的参数空间获取函数"""
        # 这里只是一个占位符，实际使用时应该从config.py导入
        return {}

# 尝试导入绘图工具
try:
    from plot_utils import plot_hyperparameter_search, save_plot
except ImportError:
    # 简单的绘图函数
    def plot_hyperparameter_search(param_name, param_values, cv_results, model_name, ax=None):
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))
        else:
            fig = ax.figure
        
        ax.plot(param_values, cv_results, 'o-')
        ax.set_xlabel(param_name)
        ax.set_ylabel('Cross-validation score')
        ax.set_title(f'Hyperparameter tuning: {model_name} - {param_name}')
        ax.grid(True, alpha=0.3)
        
        # 标记最佳值
        best_idx = np.argmax(cv_results)
        best_value = param_values[best_idx]
        best_score = cv_results[best_idx]
        
        ax.scatter([best_value], [best_score], s=100, c='r', marker='*',
                  label=f'Best: {best_value} (score: {best_score:.4f})')
        ax.legend()
        
        return fig, ax
    
    def save_plot(fig, model_name, plot_type, file_name=None, close_fig=True):
        if file_name is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            file_name = f"{model_name}_{plot_type}_{timestamp}.{SAVE_FORMAT}"
        
        # 确保输出目录存在
        model_dir = OUTPUT_PATH / model_name
        model_dir.mkdir(parents=True, exist_ok=True)
        
        save_path = model_dir / file_name
        fig.savefig(save_path, dpi=150, bbox_inches='tight')
        
        if close_fig:
            plt.close(fig)
        
        return str(save_path)

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("hyperparameter_tuning")
except ImportError:
    import logging
    logger = logging.getLogger("hyperparameter_tuning")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 定义保存格式
SAVE_FORMAT = 'pdf'

# 导入所有可能的模型类
# 这些导入放在这里是为了避免在没有使用特定模型时也加载它们
def import_model_class(model_name):
    """根据模型名称导入对应的模型类"""
    if model_name == "XGBoost":
        from xgboost import XGBClassifier
        return XGBClassifier
    elif model_name == "LightGBM":
        from lightgbm import LGBMClassifier
        return LGBMClassifier
    elif model_name == "CatBoost":
        from catboost import CatBoostClassifier
        return CatBoostClassifier
    elif model_name == "RandomForest":
        from sklearn.ensemble import RandomForestClassifier
        return RandomForestClassifier
    elif model_name == "DecisionTree":
        from sklearn.tree import DecisionTreeClassifier
        return DecisionTreeClassifier
    elif model_name == "Logistic":
        from sklearn.linear_model import LogisticRegression
        return LogisticRegression
    elif model_name == "SVM":
        from sklearn.svm import SVC
        return SVC
    elif model_name == "KNN":
        from sklearn.neighbors import KNeighborsClassifier
        return KNeighborsClassifier
    elif model_name == "NaiveBayes":
        from sklearn.naive_bayes import GaussianNB
        return GaussianNB
    elif model_name == "NeuralNet":
        from sklearn.neural_network import MLPClassifier
        return MLPClassifier
    else:
        raise ValueError(f"不支持的模型类型: {model_name}")

class HyperparameterTuner:
    """超参数调优类"""
    def __init__(self, model_name, n_trials=50, X_train=None, y_train=None, cv_folds=5, dataset_type=None, custom_params=None):
        self.model_name = model_name
        self.n_trials = n_trials
        self.X_train = X_train
        self.y_train = y_train
        self.cv_folds = cv_folds
        self.study = None
        self.best_params = {}
        self.best_score = 0.0
        
        # 如果未指定数据集类型，则自动判断
        if dataset_type is None and X_train is not None:
            self.dataset_type = get_dataset_type(X_train)
        else:
            self.dataset_type = dataset_type or 'default'
            
        # 获取超参数搜索空间
        self.param_space = get_optuna_param_space(model_name, self.dataset_type, custom_params)
        
        # 导入模型类
        try:
            self.model_class = import_model_class(model_name)
        except Exception as e:
            logger.error(f"导入模型类失败: {e}")
            self.model_class = None

    def _create_model_with_params(self, params):
        """使用给定参数创建模型实例"""
        # 为不同模型添加特定的配置
        if self.model_name == "XGBoost":
            # GPU配置 - 使用XGBoost 2.0+语法并检查GPU可用性
            if USE_GPU and GPU_INFO.get('xgboost_gpu', False):
                try:
                    params["device"] = "cuda"
                    params["tree_method"] = "hist"
                    logger.info("XGBoost使用GPU加速")
                except Exception as e:
                    logger.warning(f"XGBoost GPU配置失败，回退到CPU: {e}")
                    params["device"] = "cpu"
                    params["tree_method"] = "hist"
            else:
                params["device"] = "cpu"
                params["tree_method"] = "hist"
            
            # 添加随机种子
            return self.model_class(**params, random_state=RANDOM_SEED, eval_metric='logloss')
            
        elif self.model_name == "LightGBM":
            # GPU配置 - 检查LightGBM GPU支持
            if USE_GPU and GPU_INFO.get('lightgbm_gpu', False):
                try:
                    params["device"] = "gpu"
                    params["gpu_platform_id"] = 0
                    params["gpu_device_id"] = GPU_IDS[0]
                    logger.info("LightGBM使用GPU加速")
                except Exception as e:
                    logger.warning(f"LightGBM GPU配置失败，回退到CPU: {e}")
                    params["device"] = "cpu"
            else:
                params["device"] = "cpu"
            
            # 添加静默模式以减少输出
            params["verbose"] = -1
            
            return self.model_class(**params, random_state=RANDOM_SEED)
            
        elif self.model_name == "CatBoost":
            # GPU配置 - 检查CatBoost GPU支持
            if USE_GPU and GPU_INFO.get('catboost_gpu', False):
                try:
                    params["task_type"] = "GPU"
                    params["devices"] = str(GPU_IDS[0])
                    logger.info("CatBoost使用GPU加速")
                except Exception as e:
                    logger.warning(f"CatBoost GPU配置失败，回退到CPU: {e}")
                    params["task_type"] = "CPU"
            else:
                params["task_type"] = "CPU"
            
            # 设置静默模式
            params["verbose"] = False
            params["allow_writing_files"] = False
            
            return self.model_class(**params, random_state=RANDOM_SEED)
            
        elif self.model_name in ["RandomForest", "DecisionTree"]:
            return self.model_class(**params, random_state=RANDOM_SEED)
            
        elif self.model_name == "Logistic":
            # 对于逻辑回归，添加更大的迭代次数以确保收敛
            return self.model_class(**params, random_state=RANDOM_SEED, max_iter=2000)
            
        elif self.model_name == "SVM":
            # 对于SVM，确保启用概率估计
            return self.model_class(**params, random_state=RANDOM_SEED, probability=True)
            
        elif self.model_name == "KNN":
            # KNN没有随机状态
            return self.model_class(**params)
            
        elif self.model_name == "NeuralNet":
            # 对于神经网络，添加更大的迭代次数
            return self.model_class(**params, random_state=RANDOM_SEED, max_iter=2000)
            
        elif self.model_name == "NaiveBayes":
            # 朴素贝叶斯没有随机状态
            return self.model_class(**params)
            
        else:
            # 默认情况
            try:
                return self.model_class(**params, random_state=RANDOM_SEED)
            except:
                return self.model_class(**params)

    def objective(self, trial):
        """通用的目标函数，适用于所有模型类型"""
        # 如果模型类导入失败，返回最小分数
        if self.model_class is None:
            return 0.0
            
        # 检查参数空间是否为空
        if not self.param_space:
            logger.warning(f"模型 {self.model_name} 的参数空间为空，使用默认参数")
            params = {}
        else:
            # 根据参数空间定义构建参数字典
            params = {}
            for param_name, param_config in self.param_space.items():
                param_type = param_config.get('type')
                
                if param_type == 'int':
                    params[param_name] = trial.suggest_int(
                        param_name, 
                        param_config.get('low'), 
                        param_config.get('high')
                    )
                elif param_type == 'float':
                    params[param_name] = trial.suggest_float(
                        param_name, 
                        param_config.get('low'), 
                        param_config.get('high'),
                        log=param_config.get('log', False)
                    )
                elif param_type == 'categorical':
                    params[param_name] = trial.suggest_categorical(
                        param_name, 
                        param_config.get('choices', [])
                    )
        
        # 创建模型
        try:
            model = self._create_model_with_params(params)
            
            # 使用交叉验证评估模型
            cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=RANDOM_SEED)
            scores = cross_val_score(
                model, 
                self.X_train, 
                self.y_train, 
                cv=cv, 
                scoring="roc_auc"
            )
            return scores.mean()
        except Exception as e:
            logger.warning(f"模型评估失败，参数: {params}, 错误: {e}")
            # 返回一个很低的分数，这样这组参数就不会被选中
            return 0.0

    def plot_optimization_history(self, study):
        """
        绘制优化历史

        Args:
            study: Optuna study对象
        """
        try:
            # 绘制优化历史
            fig = optuna.visualization.plot_optimization_history(study)
            fig.update_layout(title=f"{self.model_name} 优化历史")

            # 保存图像
            model_dir = OUTPUT_PATH / self.model_name
            model_dir.mkdir(parents=True, exist_ok=True)

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            file_path = model_dir / f"optimization_history_{timestamp}.{SAVE_FORMAT}"

            # 尝试使用kaleido引擎保存图像
            try:
                fig.write_image(str(file_path), engine="kaleido")
                logger.info(f"优化历史图已保存到: {file_path}")
            except Exception as e:
                logger.warning(f"使用kaleido引擎保存图像失败: {e}")
                # 尝试使用其他方法保存
                try:
                    fig.write_html(str(file_path).replace(f'.{SAVE_FORMAT}', '.html'))
                    logger.info(f"优化历史图已保存为HTML格式到: {str(file_path).replace(f'.{SAVE_FORMAT}', '.html')}")
                except Exception as e2:
                    logger.warning(f"保存HTML格式也失败: {e2}")

        except Exception as e:
            logger.warning(f"绘制优化历史图失败: {e}")

    def plot_param_importances(self, study):
        """
        绘制超参数重要性

        Args:
            study: Optuna study对象
        """
        try:
            # 绘制超参数重要性
            fig = optuna.visualization.plot_param_importances(study)
            fig.update_layout(title=f"{self.model_name} 超参数重要性")

            # 保存图像
            model_dir = OUTPUT_PATH / self.model_name
            model_dir.mkdir(parents=True, exist_ok=True)

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            file_path = model_dir / f"param_importances_{timestamp}.{SAVE_FORMAT}"

            # 尝试使用kaleido引擎保存图像
            try:
                fig.write_image(str(file_path), engine="kaleido")
                logger.info(f"超参数重要性图已保存到: {file_path}")
            except Exception as e:
                logger.warning(f"使用kaleido引擎保存图像失败: {e}")
                # 尝试使用其他方法保存
                try:
                    fig.write_html(str(file_path).replace(f'.{SAVE_FORMAT}', '.html'))
                    logger.info(f"超参数重要性图已保存为HTML格式到: {str(file_path).replace(f'.{SAVE_FORMAT}', '.html')}")
                except Exception as e2:
                    logger.warning(f"保存HTML格式也失败: {e2}")

        except Exception as e:
            logger.warning(f"绘制超参数重要性图失败: {e}")

    def save_tuning_visualization_to_pdf(self, study, viz_type, output_path=None):
        """
        将超参数调优的可视化结果保存为PDF文件

        Args:
            study: Optuna study对象
            viz_type: 可视化类型，可选 'optimization_history', 'param_importances'
            output_path: 输出路径，如果为None则使用默认路径

        Returns:
            str: 保存的文件路径
        """
        try:
            # 确定保存路径
            if output_path is None:
                model_dir = OUTPUT_PATH / self.model_name
                model_dir.mkdir(parents=True, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                plot_type = viz_type if viz_type else "tuning_viz"
                output_path = model_dir / f"{plot_type}_{timestamp}.pdf"
            
            # 尝试使用plotly+kaleido保存
            try:
                # 创建图形
                if viz_type == 'optimization_history':
                    fig = optuna.visualization.plot_optimization_history(study)
                    fig.update_layout(title=f"{self.model_name} 优化历史")
                    plot_type = "optimization_history"
                elif viz_type == 'param_importances':
                    fig = optuna.visualization.plot_param_importances(study)
                    fig.update_layout(title=f"{self.model_name} 超参数重要性")
                    plot_type = "param_importances"
                elif viz_type == 'slice':
                    # 获取最重要的参数
                    importance = optuna.importance.get_param_importances(study)
                    if not importance:
                        logger.warning("无法获取参数重要性")
                        return None
                    # 选择最重要的参数
                    most_important_param = list(importance.keys())[0]
                    fig = optuna.visualization.plot_slice(study, params=[most_important_param])
                    fig.update_layout(title=f"{self.model_name} 参数切片 - {most_important_param}")
                    plot_type = "param_slice"
                else:
                    logger.warning(f"不支持的可视化类型: {viz_type}")
                    return None
                
                # 保存图像
                fig.write_image(str(output_path), engine="kaleido", format='pdf')
                logger.info(f"可视化图表已保存到: {output_path}")
                return str(output_path)
            except Exception as e:
                logger.warning(f"使用kaleido引擎保存PDF失败: {e}")
                
                # 尝试使用matplotlib备选方案
                try:
                    # 创建matplotlib图形
                    import matplotlib.pyplot as plt
                    import numpy as np
                    
                    plt.figure(figsize=(10, 6))
                    
                    if viz_type == 'optimization_history':
                        # 提取试验数据
                        trials = study.trials
                        trial_numbers = [t.number for t in trials if t.state.is_finished()]
                        values = [t.value for t in trials if t.state.is_finished() and t.value is not None]
                        
                        # 计算累积最佳值
                        best_values = []
                        current_best = float('-inf')
                        for value in values:
                            if value > current_best:
                                current_best = value
                            best_values.append(current_best)
                        
                        # 绘制图表
                        plt.plot(trial_numbers, values, 'o-', alpha=0.6, label='试验值')
                        plt.plot(trial_numbers, best_values, 'r-', linewidth=2, label='最佳值')
                        plt.xlabel('试验次数')
                        plt.ylabel('目标值')
                        plt.title(f'{self.model_name} - 优化历史')
                        plt.legend()
                        plt.grid(True, alpha=0.3)
                        
                    elif viz_type == 'param_importances':
                        # 计算参数重要性
                        importance = optuna.importance.get_param_importances(study)
                        if not importance:
                            plt.text(0.5, 0.5, "无法计算参数重要性", 
                                    ha='center', va='center', transform=plt.gca().transAxes)
                        else:
                            # 准备数据
                            params = list(importance.keys())
                            importances = list(importance.values())
                            
                            # 创建水平条形图
                            y_pos = np.arange(len(params))
                            plt.barh(y_pos, importances)
                            plt.yticks(y_pos, params)
                            plt.xlabel('重要性')
                            plt.title(f'{self.model_name} - 参数重要性')
                            plt.grid(True, alpha=0.3)
                            
                    elif viz_type == 'slice':
                        # 获取最重要的参数
                        importance = optuna.importance.get_param_importances(study)
                        if not importance:
                            plt.text(0.5, 0.5, "无法获取参数重要性", 
                                    ha='center', va='center', transform=plt.gca().transAxes)
                        else:
                            # 选择最重要的参数
                            most_important_param = list(importance.keys())[0]
                            
                            # 提取该参数的所有值和对应的目标值
                            param_values = []
                            objective_values = []
                            
                            for trial in study.trials:
                                if trial.state.is_finished() and most_important_param in trial.params:
                                    param_values.append(trial.params[most_important_param])
                                    objective_values.append(trial.value)
                            
                            # 绘制散点图
                            plt.scatter(param_values, objective_values, alpha=0.6)
                            plt.xlabel(most_important_param)
                            plt.ylabel('目标值')
                            plt.title(f'{self.model_name} - 参数切片 ({most_important_param})')
                            plt.grid(True, alpha=0.3)
                    
                    # 保存图表
                    plt.tight_layout()
                    plt.savefig(output_path, dpi=300, bbox_inches='tight')
                    plt.close()
                    
                    logger.info(f"使用matplotlib保存图表到: {output_path}")
                    return str(output_path)
                    
                except Exception as e2:
                    logger.warning(f"使用matplotlib保存PDF失败: {e2}")
                    
                    # 最后尝试保存为HTML
                    try:
                        html_path = str(output_path).replace('.pdf', '.html')
                        fig.write_html(html_path)
                        logger.info(f"可视化图表已保存为HTML格式到: {html_path}")
                        return html_path
                    except Exception as e3:
                        logger.warning(f"保存HTML格式也失败: {e3}")
                        return None
        except Exception as e:
            logger.warning(f"保存可视化图表失败: {e}")
            return None

    def tune_model(self, return_study=False):
        """
        对指定模型进行超参数调优

        Args:
            return_study: 是否返回study对象用于可视化

        Returns:
            tuple: (最佳参数, 最佳得分) 或 (最佳参数, 最佳得分, study对象)
        """
        # 检查是否有参数空间
        if not self.param_space:
            logger.warning(f"模型 {self.model_name} 没有定义参数空间，跳过调优")
            if return_study:
                return {}, 0.0, None
            return {}, 0.0
        
        # 检查是否支持该模型
        if self.model_class is None:
            logger.error(f"不支持的模型类型: {self.model_name}")
            if return_study:
                return {}, 0.0, None
            return {}, 0.0
        
        # 朴素贝叶斯几乎没有需要调优的参数
        if self.model_name == "NaiveBayes":
            logger.info("NaiveBayes模型参数较少，可能不需要复杂调优")
            # 我们仍然可以尝试调优var_smoothing参数
        
        logger.info(f"开始对 {self.model_name} 进行超参数调优，试验次数: {self.n_trials}，数据集类型: {self.dataset_type}")
        start_time = time.time()

        # 创建study对象，设置方向为最大化得分
        self.study = optuna.create_study(direction="maximize")

        # 运行优化
        try:
            self.study.optimize(self.objective, n_trials=self.n_trials)

            # 记录最佳参数和得分
            self.best_params = self.study.best_params
            self.best_score = self.study.best_value

            # 打印结果
            logger.info(f"模型 {self.model_name} 的最佳参数: {self.best_params}")
            logger.info(f"模型 {self.model_name} 的最佳得分: {self.best_score:.4f}")

            # 绘制优化历史和参数重要性
            try:
                self.plot_optimization_history(self.study)
                self.plot_param_importances(self.study)
            except Exception as e:
                logger.warning(f"绘制优化可视化失败: {e}")

            # 记录调优耗时
            end_time = time.time()
            logger.info(f"超参数调优完成，耗时: {end_time - start_time:.2f} 秒")

            if return_study:
                return self.best_params, self.best_score, self.study
            return self.best_params, self.best_score

        except Exception as e:
            logger.error(f"超参数调优过程中出错: {e}")
            if return_study:
                return {}, 0.0, None
            return {}, 0.0

# 便捷函数，用于直接调用调优
def tune_model(model_name, X_train=None, y_train=None, n_trials=None, cv_folds=None, 
              dataset_type=None, custom_params=None, tuning_mode='standard', return_study=False):
    """
    便捷函数，用于直接调用超参数调优
    
    Args:
        model_name: 模型名称
        X_train: 训练特征数据
        y_train: 训练标签数据
        n_trials: 试验次数，如果为None则根据tuning_mode确定
        cv_folds: 交叉验证折数，如果为None则根据tuning_mode确定
        dataset_type: 数据集类型，如果为None则自动判断
        custom_params: 自定义参数空间，用于覆盖默认参数
        tuning_mode: 调优模式，可选 'quick', 'standard', 'deep'
        return_study: 是否返回study对象
        
    Returns:
        tuple: (最佳参数, 最佳得分) 或 (最佳参数, 最佳得分, study对象)
    """
    # 根据调优模式确定n_trials和cv_folds
    if tuning_mode == 'quick':
        config = QUICK_TUNING_CONFIG
    elif tuning_mode == 'deep':
        config = DEEP_TUNING_CONFIG
    else:  # 'standard'
        config = STANDARD_TUNING_CONFIG
    
    # 如果未指定，则使用配置中的值
    n_trials = n_trials or config.get('n_trials')
    cv_folds = cv_folds or config.get('cv_folds')
    
    # 创建调优器并执行调优
    tuner = HyperparameterTuner(
        model_name=model_name,
        n_trials=n_trials,
        X_train=X_train,
        y_train=y_train,
        cv_folds=cv_folds,
        dataset_type=dataset_type,
        custom_params=custom_params
    )
    
    return tuner.tune_model(return_study=return_study)

# 如果直接运行该脚本，执行测试
if __name__ == "__main__":
    # 生成一些随机测试数据
    np.random.seed(RANDOM_SEED)
    X_test = np.random.rand(100, 5)
    y_test = np.random.randint(0, 2, 100)
    
    # 测试调优XGBoost模型
    best_params, best_score = tune_model(
        model_name="XGBoost",
        X_train=X_test,
        y_train=y_test,
        tuning_mode='quick'  # 使用快速模式进行测试
    )
    
    print(f"最佳参数: {best_params}")
    print(f"最佳得分: {best_score:.4f}")
