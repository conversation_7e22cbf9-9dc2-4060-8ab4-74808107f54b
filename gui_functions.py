#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能实现模块
包含GUI的所有功能实现
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os
import sys
import time
import traceback
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from pathlib import Path
import platform
import subprocess
import json
import webbrowser
from datetime import datetime

# 尝试导入数据预处理模块
try:
    from code.data_preprocessing import DataPreprocessor
except ImportError:
    print("警告: 无法导入数据预处理模块")

import matplotlib
matplotlib.use('TkAgg')  # 设置matplotlib后端
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import optuna

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

from data_preprocessing import DataPreprocessor, load_and_clean_data
from model_training import MODEL_TRAINERS
from plot_utils import PlotManager

class GUIFunctions:
    """GUI功能实现类"""
    
    def __init__(self, gui_instance):
        """
        初始化GUI功能
        
        Args:
            gui_instance: GUI主界面实例
        """
        self.gui = gui_instance
        self.current_data = None
        self.current_target_col = None
        self.trained_models = {}
        self.training_thread = None
        self.is_training = False
        
    def load_data(self):
        """加载数据的完整实现"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.gui.current_data_path.set(file_path)
            self.gui.load_data_file()
    
    def start_training(self):
        """开始模型训练"""
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return
        
        # 检查数据是否已加载
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return
        
        # 检查是否选择了模型
        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return
        
        # 在新线程中执行训练
        self.training_thread = threading.Thread(target=self._train_models_thread, args=(selected_models,))
        self.training_thread.daemon = True
        self.training_thread.start()
    
    def _train_models_thread(self, selected_models):
        """在后台线程中训练模型"""
        try:
            self.is_training = True
            self.gui.status_text.set("正在训练模型...")
            self.gui.log_message("开始模型训练")

            # 准备数据
            data_path = self.gui.current_data_path.get()
            test_size = self.gui.test_size_var.get()
            random_seed = self.gui.random_seed_var.get()
            scaling_method = self.gui.scaling_var.get()

            # 获取超参数调优配置
            tuning_config = self.gui.get_tuning_config()
            enable_tuning = tuning_config.get('enabled', False)

            if enable_tuning:
                self.gui.log_message("已启用超参数调优，将在训练前进行参数优化")
            else:
                self.gui.log_message("未启用超参数调优，使用默认参数训练")

            # 创建数据预处理器
            preprocessor = DataPreprocessor(
                test_size=test_size,
                random_state=random_seed,
                scaling_method=scaling_method if scaling_method != "none" else None
            )

            # 加载和预处理数据
            X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess(data_path)

            # 训练选中的模型
            total_models = len(selected_models)
            for i, model_name in enumerate(selected_models):
                if not self.is_training:  # 检查是否被停止
                    break

                self.gui.log_message(f"正在训练 {model_name}...")
                progress = (i / total_models) * 100
                self.gui.training_progress.set(progress)

                try:
                    # 获取训练器
                    trainer = MODEL_TRAINERS[model_name]
                    best_params = None

                    # 如果启用了超参数调优，先进行调优
                    if enable_tuning:
                        self.gui.log_message(f"  🔧 正在对 {model_name} 进行超参数调优...")

                        try:
                            # 导入调优模块
                            from code.hyperparameter_tuning import tune_model

                            # 确定数据集类型
                            dataset_type = 'balanced'
                            if hasattr(y_train, 'value_counts'):
                                class_counts = y_train.value_counts()
                            else:
                                import pandas as pd
                                class_counts = pd.Series(y_train).value_counts()

                            if len(class_counts) > 1:
                                ratio = class_counts.min() / class_counts.max()
                                if ratio < 0.5:
                                    dataset_type = 'imbalanced'

                            # 确定调优模式
                            tuning_mode = 'standard'
                            n_trials = tuning_config.get('n_trials', 50)
                            if tuning_config.get('quick_mode', False):
                                tuning_mode = 'quick'
                                n_trials = min(n_trials, 20)
                            elif tuning_config.get('deep_mode', False):
                                tuning_mode = 'deep'
                                n_trials = max(n_trials, 100)

                            self.gui.log_message(f"    数据集类型: {dataset_type}, 调优模式: {tuning_mode}, 试验次数: {n_trials}")

                            # 执行超参数调优
                            best_params, best_score = tune_model(
                                model_name=model_name,
                                X_train=X_train,
                                y_train=y_train,
                                n_trials=n_trials,
                                dataset_type=dataset_type,
                                tuning_mode=tuning_mode
                            )

                            self.gui.log_message(f"  ✅ {model_name} 调优完成，最佳得分: {best_score:.4f}")

                            # 简化参数显示
                            if best_params:
                                param_summary = []
                                for key, value in list(best_params.items())[:3]:  # 只显示前3个参数
                                    if isinstance(value, float):
                                        param_summary.append(f"{key}={value:.3f}")
                                    else:
                                        param_summary.append(f"{key}={value}")
                                param_str = ", ".join(param_summary)
                                if len(best_params) > 3:
                                    param_str += f" (共{len(best_params)}个参数)"
                                self.gui.log_message(f"    主要参数: {param_str}")

                        except Exception as tuning_error:
                            self.gui.log_message(f"  ❌ {model_name} 调优失败: {tuning_error}，使用默认参数")
                            best_params = None

                    # 使用最佳参数（如果有）训练模型
                    model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test, params=best_params)

                    # 保存训练好的模型（包含训练数据以支持学习曲线）
                    self.trained_models[model_name] = {
                        'model': model,
                        'trainer': trainer,
                        'X_train': X_train,
                        'y_train': y_train,
                        'X_test': X_test,
                        'y_test': y_test,
                        'best_params': best_params,  # 保存最佳参数
                        'tuning_enabled': enable_tuning
                    }

                    if best_params:
                        self.gui.log_message(f"🎯 {model_name} 训练完成（使用调优参数）")
                    else:
                        self.gui.log_message(f"📝 {model_name} 训练完成（使用默认参数）")

                except Exception as e:
                    self.gui.log_message(f"{model_name} 训练失败: {e}")

            # 训练完成
            self.gui.training_progress.set(100)

            # 统计调优和非调优模型数量
            tuned_count = sum(1 for model_data in self.trained_models.values()
                            if model_data.get('best_params') is not None)
            total_count = len(self.trained_models)

            if enable_tuning and tuned_count > 0:
                self.gui.status_text.set(f"训练完成，共训练 {total_count} 个模型（{tuned_count} 个使用调优参数）")
                self.gui.log_message(f"🎉 所有模型训练完成！共 {total_count} 个模型，其中 {tuned_count} 个使用了超参数调优")
            else:
                self.gui.status_text.set(f"训练完成，共训练 {total_count} 个模型")
                self.gui.log_message(f"📋 所有模型训练完成！共训练 {total_count} 个模型")

            # 更新可视化模型选择列表
            self._update_viz_model_list()

        except Exception as e:
            self.gui.log_message(f"训练过程出错: {e}")
            messagebox.showerror("错误", f"训练失败: {e}")
        finally:
            self.is_training = False
    
    def stop_training(self):
        """停止模型训练"""
        if self.is_training:
            self.is_training = False
            self.gui.log_message("用户停止了训练")
            self.gui.status_text.set("训练已停止")
    
    def _update_viz_model_list(self):
        """更新可视化模型选择列表"""
        model_names = list(self.trained_models.keys())
        self.gui.viz_model_combo['values'] = model_names
        if model_names:
            self.gui.viz_model_var.set(model_names[0])
    
    def single_model_visualization(self):
        """单模型可视化（完善版）"""
        selected_model = self.gui.viz_model_var.get()
        self.gui.log_message(f"开始单模型可视化，选择的模型: {selected_model}")
        self.gui.log_message(f"当前已训练模型: {list(self.trained_models.keys())}")

        if not selected_model:
            messagebox.showwarning("警告", "请选择一个模型")
            self.gui.log_message("警告：未选择模型")
            return

        if selected_model not in self.trained_models:
            messagebox.showwarning("警告", "请先训练模型或选择有效的模型")
            self.gui.log_message(f"警告：模型 {selected_model} 未找到")
            return

        try:
            # 获取模型数据
            model_data = self.trained_models[selected_model]
            model = model_data['model']
            X_test = model_data['X_test']
            y_test = model_data['y_test']
            X_train = model_data.get('X_train', None)
            y_train = model_data.get('y_train', None)

            self.gui.log_message(f"模型数据获取成功，测试集大小: {X_test.shape}")

            # 创建可视化
            self._create_model_visualization(selected_model, model, X_test, y_test, X_train, y_train)

        except Exception as e:
            error_msg = f"可视化失败: {e}"
            messagebox.showerror("错误", error_msg)
            self.gui.log_message(error_msg)
            import traceback
            traceback.print_exc()

    def _create_model_visualization(self, model_name, model, X_test, y_test, X_train=None, y_train=None):
        """创建模型可视化图表（完善版）"""
        chart_type = self.gui.chart_type_var.get()
        self.gui.log_message(f"创建图表: {chart_type} for {model_name}")

        try:
            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(12, 8))

            if chart_type == "ROC曲线":
                self.gui.log_message("绘制ROC曲线...")
                self._plot_roc_curve(ax, model, X_test, y_test, model_name)
            elif chart_type == "混淆矩阵":
                self.gui.log_message("绘制混淆矩阵...")
                self._plot_confusion_matrix(ax, model, X_test, y_test, model_name)
            elif chart_type == "特征重要性":
                self.gui.log_message("绘制特征重要性...")
                self._plot_feature_importance(ax, model, model_name, X_test)
            elif chart_type == "学习曲线":
                if X_train is not None and y_train is not None:
                    self.gui.log_message("绘制学习曲线...")
                    self._plot_learning_curve(ax, model, X_train, y_train, model_name)
                else:
                    self.gui.log_message("学习曲线需要训练数据")
                    ax.text(0.5, 0.5, "学习曲线需要训练数据，请重新训练模型",
                           ha='center', va='center', transform=ax.transAxes, fontsize=14)
            elif chart_type == "性能比较":
                self.gui.log_message("绘制性能雷达图...")
                self._plot_performance_metrics(ax, model, X_test, y_test, model_name)
            elif chart_type == "SHAP分析":
                self.gui.log_message("绘制SHAP分析...")
                self._plot_shap_analysis(ax, model, X_test, model_name)
            else:
                self.gui.log_message(f"未实现的图表类型: {chart_type}")
                ax.text(0.5, 0.5, f"图表类型 '{chart_type}' 暂未实现",
                       ha='center', va='center', transform=ax.transAxes, fontsize=14)

            self.gui.log_message("图表绘制完成，开始显示...")

            # 在GUI中显示图表
            self._display_chart_in_gui(fig)

        except Exception as e:
            self.gui.log_message(f"图表创建失败: {e}")
            raise
    
    def _plot_roc_curve(self, ax, model, X_test, y_test, model_name):
        """绘制ROC曲线（改进版）"""
        from sklearn.metrics import roc_curve, auc, classification_report
        import numpy as np

        # 获取预测概率
        if hasattr(model, 'predict_proba'):
            y_pred_proba = model.predict_proba(X_test)[:, 1]
        else:
            y_pred_proba = model.decision_function(X_test)
            # 将decision_function的输出转换为概率
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
            y_pred_proba = scaler.fit_transform(y_pred_proba.reshape(-1, 1)).flatten()

        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(y_test, y_pred_proba)
        roc_auc = auc(fpr, tpr)

        # 检查数据质量
        class_distribution = np.bincount(y_test)
        is_balanced = min(class_distribution) / max(class_distribution) > 0.3

        # 选择颜色（根据AUC质量）
        if roc_auc >= 0.8:
            color = 'green'
        elif roc_auc >= 0.7:
            color = 'orange'
        else:
            color = 'red'

        # 绘制ROC曲线
        ax.plot(fpr, tpr, color=color, lw=2,
               label=f'{model_name} (AUC = {roc_auc:.3f})')
        ax.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.5, label='Random')

        # 添加最优工作点
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        ax.plot(fpr[optimal_idx], tpr[optimal_idx], 'ro', markersize=8,
               label=f'Optimal (t={optimal_threshold:.3f})')

        # 设置图形属性
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        ax.set_xlabel('False Positive Rate (1 - Specificity)')
        ax.set_ylabel('True Positive Rate (Sensitivity)')

        # 根据数据质量调整标题
        balance_info = "Balanced" if is_balanced else "Imbalanced"
        ax.set_title(f'ROC Curve - {model_name}\n({balance_info} Data, n={len(y_test)})')

        ax.legend(loc="lower right", fontsize=9)
        ax.grid(True, alpha=0.3)

        # 添加性能提示
        if roc_auc < 0.6:
            ax.text(0.6, 0.2, 'Poor Performance\nConsider:\n• Feature engineering\n• Different algorithm\n• Data balancing',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                   fontsize=8, verticalalignment='top')
        elif not is_balanced and roc_auc < 0.8:
            ax.text(0.6, 0.2, 'Imbalanced Data\nConsider:\n• Class weighting\n• Resampling\n• Different metrics',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
                   fontsize=8, verticalalignment='top')
    
    def _plot_confusion_matrix(self, ax, model, X_test, y_test, model_name):
        """Plot Confusion Matrix"""
        from sklearn.metrics import confusion_matrix
        import seaborn as sns

        y_pred = model.predict(X_test)
        cm = confusion_matrix(y_test, y_pred)

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                   xticklabels=['Negative', 'Positive'],
                   yticklabels=['Negative', 'Positive'])
        ax.set_title(f'Confusion Matrix - {model_name}')
        ax.set_xlabel('Predicted Label')
        ax.set_ylabel('True Label')
    
    def _plot_feature_importance(self, ax, model, model_name, X_test):
        """绘制特征重要性（改进版）"""
        try:
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_

                # 获取特征名称
                if hasattr(X_test, 'columns'):
                    feature_names = X_test.columns.tolist()
                else:
                    feature_names = [f'Feature_{i}' for i in range(len(importances))]

                # 选择前15个重要特征
                indices = np.argsort(importances)[::-1][:15]
                selected_importances = importances[indices]
                selected_features = [feature_names[i] for i in indices]

                # 创建水平条形图
                colors = plt.cm.Blues(np.linspace(0.4, 0.9, len(selected_importances)))
                bars = ax.barh(range(len(selected_importances)), selected_importances, color=colors)

                # Set labels and title
                ax.set_yticks(range(len(selected_importances)))
                ax.set_yticklabels(selected_features)
                ax.set_xlabel('Importance Score')
                ax.set_title(f'Feature Importance Analysis - {model_name}', fontsize=14, fontweight='bold')

                # Add value labels
                for i, (bar, importance) in enumerate(zip(bars, selected_importances)):
                    ax.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
                           f'{importance:.3f}', ha='left', va='center', fontsize=9)

                # Beautify chart
                ax.grid(True, alpha=0.3, axis='x')
                ax.set_axisbelow(True)
                ax.invert_yaxis()  # Most important features at top

            elif hasattr(model, 'coef_'):
                # 对于线性模型，使用系数的绝对值
                coef = np.abs(model.coef_[0] if model.coef_.ndim > 1 else model.coef_)

                # 获取特征名称
                if hasattr(X_test, 'columns'):
                    feature_names = X_test.columns.tolist()
                else:
                    feature_names = [f'Feature_{i}' for i in range(len(coef))]

                # 选择前15个重要特征
                indices = np.argsort(coef)[::-1][:15]
                selected_coef = coef[indices]
                selected_features = [feature_names[i] for i in indices]

                # 创建水平条形图
                colors = plt.cm.Reds(np.linspace(0.4, 0.9, len(selected_coef)))
                bars = ax.barh(range(len(selected_coef)), selected_coef, color=colors)

                # Set labels and title
                ax.set_yticks(range(len(selected_coef)))
                ax.set_yticklabels(selected_features)
                ax.set_xlabel('Coefficient Absolute Value')
                ax.set_title(f'Feature Coefficient Importance - {model_name}', fontsize=14, fontweight='bold')

                # Add value labels
                for i, (bar, coef_val) in enumerate(zip(bars, selected_coef)):
                    ax.text(bar.get_width() + max(selected_coef) * 0.01,
                           bar.get_y() + bar.get_height()/2,
                           f'{coef_val:.3f}', ha='left', va='center', fontsize=9)

                # Beautify chart
                ax.grid(True, alpha=0.3, axis='x')
                ax.set_axisbelow(True)
                ax.invert_yaxis()

            else:
                ax.text(0.5, 0.5, f'{model_name} does not support feature importance analysis\nSupported model types:\n• Tree models (Random Forest, XGBoost, etc.)\n• Linear models (Logistic Regression, etc.)',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

        except Exception as e:
            ax.text(0.5, 0.5, f'Feature importance analysis failed:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _plot_learning_curve(self, ax, model, X_train, y_train, model_name):
        """绘制学习曲线"""
        try:
            from sklearn.model_selection import learning_curve

            # 计算学习曲线
            train_sizes, train_scores, val_scores = learning_curve(
                model, X_train, y_train, cv=5, n_jobs=-1,
                train_sizes=np.linspace(0.1, 1.0, 10),
                scoring='roc_auc', random_state=42
            )

            # 计算均值和标准差
            train_mean = np.mean(train_scores, axis=1)
            train_std = np.std(train_scores, axis=1)
            val_mean = np.mean(val_scores, axis=1)
            val_std = np.std(val_scores, axis=1)

            # Plot learning curves
            ax.plot(train_sizes, train_mean, 'o-', color='blue', label='Training Set')
            ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.2, color='blue')

            ax.plot(train_sizes, val_mean, 'o-', color='red', label='Validation Set')
            ax.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, alpha=0.2, color='red')

            # Set labels and title
            ax.set_xlabel('Training Sample Size')
            ax.set_ylabel('AUC Score')
            ax.set_title(f'Learning Curve - {model_name}', fontsize=14, fontweight='bold')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # Add performance analysis text
            final_train_score = train_mean[-1]
            final_val_score = val_mean[-1]
            gap = final_train_score - final_val_score

            if gap > 0.1:
                analysis = "Model may be overfitting\nSuggestion: Reduce model complexity"
                color = "orange"
            elif final_val_score < 0.7:
                analysis = "Low model performance\nSuggestion: Add features or data"
                color = "red"
            else:
                analysis = "Good model performance"
                color = "green"

            ax.text(0.02, 0.98, f'Training AUC: {final_train_score:.3f}\nValidation AUC: {final_val_score:.3f}\nGap: {gap:.3f}\n\n{analysis}',
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.3),
                   fontsize=10)

        except Exception as e:
            ax.text(0.5, 0.5, f'Learning curve generation failed:\n{str(e)}\n\nPossible reasons:\n• Model does not support incremental training\n• Insufficient data\n• Cross-validation failed',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _plot_performance_metrics(self, ax, model, X_test, y_test, model_name):
        """绘制性能指标雷达图"""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

            # 计算预测结果
            y_pred = model.predict(X_test)

            # 计算各项指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred)

            # 计算AUC
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_pred_proba)
            else:
                auc = 0.5  # 默认值

            # 计算特异性
            tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

            # Prepare radar chart data
            metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC', 'Specificity']
            values = [accuracy, precision, recall, f1, auc, specificity]

            # Create radar chart
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            values += values[:1]  # Close the shape
            angles += angles[:1]

            ax.clear()
            ax = plt.subplot(111, projection='polar')
            ax.plot(angles, values, 'o-', linewidth=2, color='blue', alpha=0.7)
            ax.fill(angles, values, alpha=0.25, color='blue')

            # Set labels
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metrics)
            ax.set_ylim(0, 1)
            ax.set_title(f'Performance Metrics Radar Chart - {model_name}', fontsize=14, fontweight='bold', pad=20)

            # Add grid
            ax.grid(True)

            # Add value labels
            for angle, value, metric in zip(angles[:-1], values[:-1], metrics):
                ax.text(angle, value + 0.05, f'{value:.3f}',
                       horizontalalignment='center', fontsize=9, fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'Performance metrics chart generation failed:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def _plot_shap_analysis(self, ax, model, X_test, model_name):
        """Plot SHAP Analysis (following command-line implementation)"""
        try:
            import shap

            # Follow exact command-line logic - use full X_test dataset
            try:
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_test)
                explainer_type = "TreeExplainer"
            except Exception as e:
                self.gui.log_message(f"TreeExplainer failed: {e}, falling back to KernelExplainer")
                try:
                    explainer = shap.KernelExplainer(model.predict_proba, X_test)
                    shap_values = explainer.shap_values(X_test)
                    explainer_type = "KernelExplainer"
                except Exception as e2:
                    self.gui.log_message(f"SHAP analysis failed: {e2}")
                    raise Exception(f"SHAP analysis failed: {e2}")

            # Ensure we only use positive class (class 1) SHAP values for summary plot
            # Exact logic from command-line version
            shap_values_for_summary = None
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_summary = shap_values[1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_summary = shap_values[:, :, 1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
                shap_values_for_summary = shap_values  # Already in correct format

            if shap_values_for_summary is None:
                self.gui.log_message(f"Cannot extract SHAP values for summary plot for model '{model_name}'. Skipping SHAP analysis.")
                raise Exception(f"Cannot extract SHAP values for summary plot from model '{model_name}'")

            # Clear the axis and create SHAP summary plot exactly like command-line version
            ax.clear()

            # Create SHAP summary plot exactly like command-line version
            import matplotlib.pyplot as plt
            plt.sca(ax)  # Set current axes

            # Use exact same parameters as command-line version
            shap.summary_plot(
                shap_values_for_summary,
                X_test,
                feature_names=X_test.columns.tolist() if hasattr(X_test, 'columns') else None,
                show=False,
                max_display=15
            )

            # Set title exactly like command-line version
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()

            self.gui.log_message(f"SHAP summary plot completed for {model_name} using {explainer_type}")

        except ImportError:
            ax.text(0.5, 0.5, 'SHAP library not installed\nPlease install: pip install shap',
                   ha='center', va='center', transform=ax.transAxes, fontsize=14,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8))
        except Exception as e:
            ax.text(0.5, 0.5, f'SHAP analysis failed:\n{str(e)}\n\nPossible reasons:\n• Model not supported by SHAP\n• Data format incompatible\n• Insufficient memory\n\nSuggestions:\n• Use supported model types\n• Reduce data dimensions\n• Check data format',
                   ha='center', va='center', transform=ax.transAxes, fontsize=11,
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))

    def create_command_line_shap_plots(self, model, X_test, model_name, output_dir):
        """
        Create SHAP plots with mixed approach:
        - Keep current method: summary, decision, waterfall plots
        - Use original method: dependence, feature_importance, force plots
        """
        try:
            import shap
            from pathlib import Path
            import matplotlib.pyplot as plt

            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            # Follow exact command-line logic from plot_single_model.py
            try:
                explainer = shap.TreeExplainer(model)
                shap_values = explainer.shap_values(X_test)
            except Exception as e:
                self.gui.log_message(f"TreeExplainer failed: {e}, falling back to KernelExplainer")
                try:
                    explainer = shap.KernelExplainer(model.predict_proba, X_test)
                    shap_values = explainer.shap_values(X_test)
                except Exception as e2:
                    self.gui.log_message(f"SHAP analysis failed: {e2}")
                    return False

            # Ensure we only use positive class (class 1) SHAP values for summary plot
            shap_values_for_summary = None
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_summary = shap_values[1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_summary = shap_values[:, :, 1]  # Positive class
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 2:
                shap_values_for_summary = shap_values  # Already in correct format

            if shap_values_for_summary is None:
                self.gui.log_message(f"Cannot extract SHAP values for summary plot for model '{model_name}'. Skipping SHAP analysis.")
                return False

            # === KEEP CURRENT METHOD ===
            # 1. SHAP Summary Plot (current command-line style)
            fig, ax = plt.subplots(figsize=(10, 6))
            shap.summary_plot(
                shap_values_for_summary,
                X_test,
                feature_names=X_test.columns.tolist() if hasattr(X_test, 'columns') else None,
                show=False,
                max_display=15
            )
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()
            summary_path = output_dir / f'{model_name}_shap_summary.{self.save_format}'
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"SHAP summary plot saved: {summary_path}")

            # 2. Decision Plot (current command-line style)
            fig, ax = plt.subplots(figsize=(10, 6))

            # Handle X_test format for decision plot
            if isinstance(X_test, np.ndarray):
                import pandas as pd
                feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
                X_test_df = pd.DataFrame(X_test, columns=feature_names)
                X_test_subset = X_test_df.iloc[:50]
            else:
                X_test_subset = X_test.iloc[:50]

            if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
                expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
                shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
            else:
                shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)
            plt.title('Decision Plot (Top 50 Samples)')
            plt.tight_layout()
            decision_path = output_dir / f"{model_name}_decision_plot.{self.save_format}"
            plt.savefig(decision_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"Decision plot saved: {decision_path}")

            # 3. Waterfall Plots (current command-line style)
            for idx in [0, 5, 10]:
                fig, ax = plt.subplots(figsize=(10, 6))

                # Handle X_test data format
                if isinstance(X_test, np.ndarray):
                    sample_data = X_test[idx]
                    feature_names = [f'feature_{i}' for i in range(X_test.shape[1])]
                else:
                    sample_data = X_test.iloc[idx].values
                    feature_names = list(X_test.columns)

                if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
                    expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                    shap_val = shap_values[..., 1][idx] if shap_values.ndim == 3 else shap_values[1][idx]
                    exp = shap.Explanation(values=shap_val,
                                           base_values=expected_val,
                                           data=sample_data,
                                           feature_names=feature_names)
                else:
                    exp = shap.Explanation(values=shap_values[idx],
                                           base_values=explainer.expected_value,
                                           data=sample_data,
                                           feature_names=feature_names)
                shap.plots.waterfall(exp, max_display=15, show=False)
                plt.title(f'Waterfall Plot (Sample {idx})')
                plt.tight_layout()
                waterfall_path = output_dir / f"{model_name}_waterfall_{idx}.{self.save_format}"
                plt.savefig(waterfall_path, dpi=300, bbox_inches='tight')
                plt.close()
                self.gui.log_message(f"Waterfall plot saved: {waterfall_path}")

            # === USE ORIGINAL METHOD ===
            # 4. Feature Importance (original enhanced_shap_visualization style)
            self._create_original_feature_importance(shap_values, X_test, model_name, output_dir)

            # 5. Dependence Plots (original enhanced_shap_visualization style)
            self._create_original_dependence_plots(shap_values, X_test, model_name, output_dir)

            # 6. Force Plots (original enhanced_shap_visualization style)
            self._create_original_force_plots(shap_values, X_test, model_name, output_dir)

            return True

        except Exception as e:
            self.gui.log_message(f"Command-line SHAP plots creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_original_feature_importance(self, shap_values, X_test, model_name, output_dir):
        """Create feature importance plot using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd

            # Handle SHAP values format
            if hasattr(shap_values, 'values'):
                importance = np.abs(shap_values.values).mean(0)
            else:
                importance = np.abs(shap_values).mean(0)

            # Handle multi-dimensional case
            if len(importance.shape) > 1:
                importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

            # Create DataFrame and sort
            feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': importance
            }).sort_values('importance', ascending=True)

            # Take top 12 important features
            top_features = importance_df.tail(12)

            # Create figure
            fig, ax = plt.subplots(figsize=(10, 8))

            # Create color gradient
            colors = plt.cm.Blues(np.linspace(0.4, 0.9, len(top_features)))

            # Draw horizontal bar chart
            bars = ax.barh(range(len(top_features)), top_features['importance'],
                          color=colors, alpha=0.8, height=0.7)

            # Ensure y-axis shows feature names
            ax.set_yticks(range(len(top_features)))
            ax.set_yticklabels(top_features['feature'], fontsize=11)

            # Add value labels
            for i, (bar, value) in enumerate(zip(bars, top_features['importance'])):
                ax.text(value + max(top_features['importance']) * 0.01, i,
                       f'{value:.3f}', va='center', ha='left', fontsize=9, fontweight='bold')

            # Set labels and title
            ax.set_xlabel('Mean |SHAP Value|', fontsize=12, fontweight='bold')
            ax.set_title(f'{model_name} - SHAP Feature Importance', fontsize=16, fontweight='bold', pad=20)

            # Add grid lines
            ax.grid(True, alpha=0.3, axis='x', linestyle='-', linewidth=0.5)
            ax.set_axisbelow(True)

            # Set background color
            ax.set_facecolor('#FAFAFA')

            # Remove top and right borders
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#CCCCCC')
            ax.spines['bottom'].set_color('#CCCCCC')

            # Set x-axis range, leave space to display values
            ax.set_xlim(0, max(top_features['importance']) * 1.15)

            plt.tight_layout()

            importance_path = output_dir / f'{model_name}_feature_importance.{self.save_format}'
            plt.savefig(importance_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            self.gui.log_message(f"Original feature importance plot saved: {importance_path}")
            return str(importance_path)

        except Exception as e:
            self.gui.log_message(f"Failed to create original feature importance plot: {e}")
            return None

    def _create_original_dependence_plots(self, shap_values, X_test, model_name, output_dir):
        """Create dependence plots using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt

            dependence_plots = []

            # Calculate feature importance
            if hasattr(shap_values, 'values'):
                importance = np.abs(shap_values.values).mean(0)
            else:
                importance = np.abs(shap_values).mean(0)

            # Handle multi-dimensional case
            if len(importance.shape) > 1:
                importance = importance.mean(1) if importance.shape[1] > 1 else importance[:, 0]

            # Get top 5 features
            top_features_idx = np.argsort(importance)[::-1][:5]

            for feature_idx in top_features_idx:
                try:
                    plt.figure(figsize=(10, 6))

                    feature_name = X_test.columns[feature_idx] if hasattr(X_test, 'columns') else f'feature_{feature_idx}'
                    feature_values = X_test.iloc[:, feature_idx].values if hasattr(X_test, 'iloc') else X_test[:, feature_idx]

                    if hasattr(shap_values, 'values'):
                        shap_vals = shap_values.values[:, feature_idx]
                    else:
                        shap_vals = shap_values[:, feature_idx]

                    # Handle multi-class case
                    if len(shap_vals.shape) > 1:
                        shap_vals = shap_vals[:, 1] if shap_vals.shape[1] > 1 else shap_vals[:, 0]

                    # Create scatter plot
                    plt.scatter(feature_values, shap_vals, alpha=0.6, s=20)
                    plt.xlabel(f'{feature_name} (Feature Value)')
                    plt.ylabel(f'SHAP Value for {feature_name}')
                    plt.title(f'{model_name} - Dependence Plot: {feature_name}')
                    plt.grid(True, alpha=0.3)

                    # Add trend line
                    try:
                        z = np.polyfit(feature_values, shap_vals, 1)
                        p = np.poly1d(z)
                        plt.plot(feature_values, p(feature_values), "r--", alpha=0.8, linewidth=2)
                    except:
                        pass

                    plt.tight_layout()

                    dependence_path = output_dir / f'{model_name}_dependence_{feature_name}.{self.save_format}'
                    plt.savefig(dependence_path, dpi=300, bbox_inches='tight', facecolor='white')
                    plt.close()

                    dependence_plots.append(str(dependence_path))
                    self.gui.log_message(f"Original dependence plot saved: {dependence_path}")

                except Exception as e:
                    self.gui.log_message(f"Failed to create dependence plot for {feature_name}: {e}")
                    continue

            return dependence_plots

        except Exception as e:
            self.gui.log_message(f"Failed to create original dependence plots: {e}")
            return []

    def _create_original_force_plots(self, shap_values, X_test, model_name, output_dir):
        """Create force plots using original enhanced_shap_visualization style"""
        try:
            import matplotlib.pyplot as plt

            force_plots = []

            # Create force plots for first 3 samples
            for i in range(min(3, len(X_test))):
                try:
                    plt.figure(figsize=(12, 4))

                    # Create force plot
                    if hasattr(shap_values, 'values'):
                        values = shap_values.values[i]
                        base_value = shap_values.base_values[i] if hasattr(shap_values, 'base_values') else 0
                    else:
                        values = shap_values[i]
                        base_value = 0

                    # Handle multi-class case
                    if len(values.shape) > 1:
                        values = values[:, 1] if values.shape[1] > 1 else values[:, 0]
                        if hasattr(base_value, '__len__') and len(base_value) > 1:
                            base_value = base_value[1] if len(base_value) > 1 else base_value[0]

                    # Manual force plot effect
                    feature_values = X_test.iloc[i].values if hasattr(X_test, 'iloc') else X_test[i]
                    feature_names = X_test.columns.tolist() if hasattr(X_test, 'columns') else [f'feature_{j}' for j in range(X_test.shape[1])]

                    # Sort features by importance
                    importance_order = np.argsort(np.abs(values))[::-1][:10]  # Top 10 important features

                    # Create bar chart showing SHAP values
                    colors = ['red' if v > 0 else 'blue' for v in values[importance_order]]
                    plt.barh(range(len(importance_order)), values[importance_order], color=colors, alpha=0.7)

                    # Add feature names and values
                    labels = [f"{feature_names[idx]} = {feature_values[idx]:.3f}" for idx in importance_order]
                    plt.yticks(range(len(importance_order)), labels)
                    plt.xlabel('SHAP Value')
                    plt.title(f'{model_name} - Force Plot (Sample {i+1})', fontsize=12)
                    plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
                    plt.grid(True, alpha=0.3)
                    plt.tight_layout()

                    force_path = output_dir / f'{model_name}_force_plot_sample_{i+1}.{self.save_format}'
                    plt.savefig(force_path, dpi=300, bbox_inches='tight', facecolor='white')
                    plt.close()

                    force_plots.append(str(force_path))
                    self.gui.log_message(f"Original force plot saved: {force_path}")

                except Exception as e:
                    self.gui.log_message(f"Failed to create force plot for sample {i+1}: {e}")
                    continue

            return force_plots

        except Exception as e:
            self.gui.log_message(f"Failed to create original force plots: {e}")
            return []

    def _display_chart_in_gui(self, fig):
        """在GUI中显示图表"""
        try:
            # 清除之前的图表
            if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
                self.gui.chart_canvas.get_tk_widget().destroy()
                self.gui.chart_canvas = None

            # 获取可视化选项卡
            viz_tab_index = 2  # 结果可视化选项卡的索引
            if len(self.gui.notebook.tabs()) > viz_tab_index:
                chart_frame = self.gui.notebook.nametowidget(self.gui.notebook.tabs()[viz_tab_index])
            else:
                self.gui.log_message("无法找到可视化选项卡")
                return

            # 找到图表显示框架
            chart_display_frame = None
            for child in chart_frame.winfo_children():
                if isinstance(child, ttk.LabelFrame) and child.cget('text') == '图表显示':
                    chart_display_frame = child
                    break

            if chart_display_frame:
                # 清除现有的图表内容（保留选项框架）
                for child in chart_display_frame.winfo_children():
                    if not isinstance(child, ttk.Frame) or not hasattr(child, '_is_options_frame'):
                        child.destroy()

                # 创建画布
                self.gui.chart_canvas = FigureCanvasTkAgg(fig, chart_display_frame)
                self.gui.chart_canvas.draw()
                self.gui.chart_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

                self.gui.log_message("图表显示成功")
            else:
                self.gui.log_message("无法找到图表显示区域")

        except Exception as e:
            self.gui.log_message(f"图表显示失败: {e}")
            import traceback
            traceback.print_exc()
    
    def model_comparison(self):
        """模型比较可视化"""
        if len(self.trained_models) < 2:
            messagebox.showwarning("警告", "需要至少训练2个模型才能进行比较")
            return
        
        try:
            self._create_comparison_chart()
        except Exception as e:
            messagebox.showerror("错误", f"模型比较失败: {e}")
    
    def _create_comparison_chart(self):
        """创建模型比较图表"""
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # 收集所有模型的性能指标
        model_names = []
        accuracies = []
        aucs = []
        
        for model_name, model_data in self.trained_models.items():
            model = model_data['model']
            X_test = model_data['X_test']
            y_test = model_data['y_test']
            
            # 计算指标
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            if hasattr(model, 'predict_proba'):
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc_score = roc_auc_score(y_test, y_pred_proba)
            else:
                auc_score = accuracy  # 如果没有概率预测，使用准确率
            
            model_names.append(model_name)
            accuracies.append(accuracy)
            aucs.append(auc_score)
        
        # 创建比较图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准确率比较
        bars1 = ax1.bar(model_names, accuracies, color='skyblue')
        ax1.set_title('Model Accuracy Comparison')
        ax1.set_ylabel('Accuracy')
        ax1.set_ylim(0, 1)
        ax1.tick_params(axis='x', rotation=45)
        
        # 在柱状图上显示数值
        for bar, acc in zip(bars1, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom')
        
        # AUC比较
        bars2 = ax2.bar(model_names, aucs, color='lightcoral')
        ax2.set_title('Model AUC Comparison')
        ax2.set_ylabel('AUC')
        ax2.set_ylim(0, 1)
        ax2.tick_params(axis='x', rotation=45)
        
        # 在柱状图上显示数值
        for bar, auc in zip(bars2, aucs):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{auc:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 在GUI中显示
        self._display_chart_in_gui(fig)
    
    def refresh_chart(self):
        """刷新当前图表"""
        if self.gui.viz_model_var.get():
            self.single_model_visualization()
    
    def save_chart(self):
        """保存当前图表"""
        if hasattr(self.gui, 'chart_canvas') and self.gui.chart_canvas:
            file_path = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("PNG files", "*.png"), ("SVG files", "*.svg"), ("All files", "*.*")]
            )
            if file_path:
                # 显示保存进度对话框
                progress_window = tk.Toplevel(self.gui.root)
                progress_window.title("保存图表")
                progress_window.geometry("300x100")
                progress_window.transient(self.gui.root)
                progress_window.grab_set()
                
                ttk.Label(progress_window, text="正在保存图表...").pack(pady=10)
                progress = ttk.Progressbar(progress_window, mode="indeterminate")
                progress.pack(fill=tk.X, padx=20, pady=10)
                progress.start()
                
                def save_thread():
                    try:
                        # 尝试使用matplotlib保存
                        fig = self.gui.chart_canvas.figure
                        fig.savefig(file_path, dpi=300, bbox_inches='tight')
                        
                        # 在主线程中更新UI
                        self.gui.root.after(0, lambda: self._show_save_result(file_path, progress_window))
                    except Exception as e:
                        # 如果失败，尝试使用其他方式保存
                        try:
                            # 创建新的图形并复制内容
                            import matplotlib.pyplot as plt
                            new_fig = plt.figure(figsize=fig.get_size_inches())
                            for i, ax_old in enumerate(fig.get_axes()):
                                # 复制轴到新图形
                                ax_new = new_fig.add_subplot(len(fig.get_axes()), 1, i+1)
                                for line in ax_old.get_lines():
                                    ax_new.plot(line.get_xdata(), line.get_ydata(), 
                                              color=line.get_color(), 
                                              linestyle=line.get_linestyle(),
                                              marker=line.get_marker(),
                                              label=line.get_label())
                                
                                # 复制标题和标签
                                ax_new.set_title(ax_old.get_title())
                                ax_new.set_xlabel(ax_old.get_xlabel())
                                ax_new.set_ylabel(ax_old.get_ylabel())
                                
                                # 复制图例
                                if ax_old.get_legend():
                                    ax_new.legend()
                                    
                                # 复制网格设置
                                ax_new.grid(ax_old.get_grid())
                            
                            # 调整布局
                            new_fig.tight_layout()
                            
                            # 保存新图形
                            new_fig.savefig(file_path, dpi=300, bbox_inches='tight')
                            plt.close(new_fig)
                            
                            # 更新UI
                            self.gui.root.after(0, lambda: self._show_save_result(file_path, progress_window))
                        except Exception as e2:
                            # 如果还是失败，显示错误
                            self.gui.root.after(0, lambda: self._show_save_error(f"{e}\n\n尝试备选方案也失败: {e2}", progress_window))
                
                # 在后台线程中执行保存操作
                import threading
                save_thread = threading.Thread(target=save_thread)
                save_thread.daemon = True
                save_thread.start()
        else:
            messagebox.showwarning("警告", "没有可保存的图表")

    def generate_detailed_report(self):
        """生成详细的单模型性能报告"""
        selected_model = self.gui.viz_model_var.get()
        if not selected_model or selected_model not in self.trained_models:
            messagebox.showwarning("警告", "请先训练模型或选择有效的模型")
            return

        try:
            # 创建报告窗口
            report_window = tk.Toplevel(self.gui.root)
            report_window.title(f"详细性能报告 - {selected_model}")
            report_window.geometry("800x600")
            report_window.transient(self.gui.root)

            # 创建文本显示区域
            text_frame = ttk.Frame(report_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            report_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=report_text.yview)
            report_text.configure(yscrollcommand=scrollbar.set)

            report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 生成报告内容
            model_data = self.trained_models[selected_model]
            model = model_data['model']
            X_test = model_data['X_test']
            y_test = model_data['y_test']

            report_content = self._generate_model_report_content(selected_model, model, X_test, y_test)

            # 显示报告
            report_text.insert(tk.END, report_content)
            report_text.config(state=tk.DISABLED)

            # 添加保存按钮
            button_frame = ttk.Frame(report_window)
            button_frame.pack(fill=tk.X, padx=10, pady=5)

            def save_report():
                file_path = filedialog.asksaveasfilename(
                    title="保存报告",
                    defaultextension=".txt",
                    filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
                )
                if file_path:
                    try:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(report_content)
                        messagebox.showinfo("成功", f"报告已保存到: {file_path}")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存报告失败: {e}")

            ttk.Button(button_frame, text="保存报告", command=save_report).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="关闭", command=report_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {e}")

    def _generate_model_report_content(self, model_name, model, X_test, y_test):
        """生成模型报告内容"""
        from sklearn.metrics import (
            accuracy_score, precision_score, recall_score, f1_score,
            roc_auc_score, confusion_matrix, classification_report
        )
        from datetime import datetime

        # 计算预测结果
        y_pred = model.predict(X_test)

        # 计算各项指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)

        # 计算AUC
        auc = 0.5
        if hasattr(model, 'predict_proba'):
            try:
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc = roc_auc_score(y_test, y_pred_proba)
            except:
                pass

        # 计算混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        tn, fp, fn, tp = cm.ravel()

        # 计算特异性
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        sensitivity = recall  # 敏感性就是召回率

        # 生成报告内容
        report = f"""
═══════════════════════════════════════════════════════════════
                    模型性能详细报告
═══════════════════════════════════════════════════════════════

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
模型名称: {model_name}
模型类型: {type(model).__name__}

═══════════════════════════════════════════════════════════════
                    数据集信息
═══════════════════════════════════════════════════════════════

测试集样本数: {len(y_test)}
特征数量: {X_test.shape[1]}
正样本数量: {np.sum(y_test == 1)}
负样本数量: {np.sum(y_test == 0)}
类别平衡比例: {np.sum(y_test == 1) / len(y_test):.3f} : {np.sum(y_test == 0) / len(y_test):.3f}

═══════════════════════════════════════════════════════════════
                    核心性能指标
═══════════════════════════════════════════════════════════════

准确率 (Accuracy):     {accuracy:.4f}
精确率 (Precision):    {precision:.4f}
召回率 (Recall):       {recall:.4f}
F1分数 (F1-Score):     {f1:.4f}
AUC分数:              {auc:.4f}
特异性 (Specificity):  {specificity:.4f}
敏感性 (Sensitivity):  {sensitivity:.4f}

═══════════════════════════════════════════════════════════════
                    混淆矩阵分析
═══════════════════════════════════════════════════════════════

混淆矩阵:
                预测值
实际值    负类    正类
  负类    {tn:4d}    {fp:4d}
  正类    {fn:4d}    {tp:4d}

真负例 (TN): {tn}
假正例 (FP): {fp}
假负例 (FN): {fn}
真正例 (TP): {tp}

═══════════════════════════════════════════════════════════════
                    性能评估
═══════════════════════════════════════════════════════════════

模型整体表现: {self._evaluate_model_performance(accuracy, precision, recall, f1, auc)}

准确率评估: {self._evaluate_metric(accuracy, "accuracy")}
精确率评估: {self._evaluate_metric(precision, "precision")}
召回率评估: {self._evaluate_metric(recall, "recall")}
F1分数评估: {self._evaluate_metric(f1, "f1")}
AUC评估: {self._evaluate_metric(auc, "auc")}

═══════════════════════════════════════════════════════════════
                    改进建议
═══════════════════════════════════════════════════════════════

{self._generate_improvement_suggestions(accuracy, precision, recall, f1, auc, tp, fp, fn, tn)}

═══════════════════════════════════════════════════════════════
                    详细分类报告
═══════════════════════════════════════════════════════════════

{classification_report(y_test, y_pred, target_names=['负类', '正类'])}

═══════════════════════════════════════════════════════════════
                    模型参数信息
═══════════════════════════════════════════════════════════════

{self._get_model_parameters(model)}

═══════════════════════════════════════════════════════════════
                    报告结束
═══════════════════════════════════════════════════════════════
"""
        return report

    def _evaluate_model_performance(self, accuracy, precision, recall, f1, auc):
        """评估模型整体性能"""
        score = (accuracy + precision + recall + f1 + auc) / 5

        if score >= 0.9:
            return "优秀 - 模型性能非常好，可以投入生产使用"
        elif score >= 0.8:
            return "良好 - 模型性能较好，可考虑进一步优化后使用"
        elif score >= 0.7:
            return "中等 - 模型性能一般，建议进行优化"
        elif score >= 0.6:
            return "较差 - 模型性能不佳，需要重新设计"
        else:
            return "很差 - 模型性能很差，建议重新选择算法或特征"

    def _evaluate_metric(self, value, metric_type):
        """评估单个指标"""
        if metric_type == "auc":
            if value >= 0.9:
                return "优秀"
            elif value >= 0.8:
                return "良好"
            elif value >= 0.7:
                return "中等"
            elif value >= 0.6:
                return "较差"
            else:
                return "很差"
        else:
            if value >= 0.95:
                return "优秀"
            elif value >= 0.85:
                return "良好"
            elif value >= 0.75:
                return "中等"
            elif value >= 0.65:
                return "较差"
            else:
                return "很差"

    def _generate_improvement_suggestions(self, accuracy, precision, recall, f1, auc, tp, fp, fn, tn):
        """生成改进建议"""
        suggestions = []

        # 基于整体性能的建议
        if accuracy < 0.8:
            suggestions.append("• 准确率较低，考虑增加更多相关特征或使用更复杂的模型")

        if precision < 0.8:
            suggestions.append("• 精确率较低，存在较多假正例，考虑调整分类阈值或使用成本敏感学习")

        if recall < 0.8:
            suggestions.append("• 召回率较低，存在较多假负例，考虑使用过采样技术或调整类别权重")

        if f1 < 0.8:
            suggestions.append("• F1分数较低，精确率和召回率需要平衡，考虑使用集成方法")

        if auc < 0.8:
            suggestions.append("• AUC较低，模型区分能力不强，考虑特征工程或模型选择")

        # 基于混淆矩阵的建议
        if fp > fn:
            suggestions.append("• 假正例多于假负例，模型倾向于过度预测正类，考虑提高分类阈值")
        elif fn > fp:
            suggestions.append("• 假负例多于假正例，模型倾向于保守预测，考虑降低分类阈值")

        # 数据平衡性建议
        total = tp + fp + fn + tn
        pos_ratio = (tp + fn) / total
        if pos_ratio < 0.1 or pos_ratio > 0.9:
            suggestions.append("• 数据集不平衡，考虑使用SMOTE、欠采样或调整类别权重")

        if not suggestions:
            suggestions.append("• 模型性能良好，可考虑进行超参数调优以进一步提升性能")

        return "\n".join(suggestions)

    def _get_model_parameters(self, model):
        """获取模型参数信息"""
        try:
            params = model.get_params()
            param_str = ""
            for key, value in params.items():
                param_str += f"{key}: {value}\n"
            return param_str
        except:
            return "无法获取模型参数信息"

    def select_best_model_automatically(self):
        """自动选择最佳模型"""
        if not self.trained_models:
            messagebox.showwarning("警告", "请先训练模型")
            return

        try:
            from best_model_selector import select_best_model_for_binary_classification

            # 让用户选择策略
            strategy_window = tk.Toplevel(self.gui.root)
            strategy_window.title("选择模型选择策略")
            strategy_window.geometry("400x300")
            strategy_window.transient(self.gui.root)
            strategy_window.grab_set()

            # 策略选择
            ttk.Label(strategy_window, text="请选择模型选择策略:", font=("Arial", 12)).pack(pady=10)

            strategy_var = tk.StringVar(value="balanced")
            strategies = [
                ("balanced", "平衡策略 - 综合考虑性能和稳健性"),
                ("performance", "性能优先 - 追求最高预测性能"),
                ("robustness", "稳健性优先 - 注重模型稳定性"),
                ("interpretability", "可解释性优先 - 便于理解和解释")
            ]

            for value, text in strategies:
                ttk.Radiobutton(strategy_window, text=text, variable=strategy_var,
                               value=value).pack(anchor=tk.W, padx=20, pady=5)

            result_text = tk.Text(strategy_window, height=8, width=50)
            result_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

            def run_selection():
                strategy = strategy_var.get()
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, "正在分析模型性能，请稍候...\n")
                strategy_window.update()

                try:
                    # 运行最佳模型选择
                    selection_result = select_best_model_for_binary_classification(
                        strategy=strategy, top_k=5
                    )

                    if selection_result:
                        # 显示结果
                        result_text.delete(1.0, tk.END)
                        result_text.insert(tk.END, f"🎯 最佳模型推荐结果\n")
                        result_text.insert(tk.END, f"{'='*40}\n")
                        result_text.insert(tk.END, f"推荐策略: {strategy}\n")
                        result_text.insert(tk.END, f"最佳模型: {selection_result['best_model']}\n")
                        result_text.insert(tk.END, f"综合得分: {selection_result['best_score']:.4f}\n\n")

                        result_text.insert(tk.END, "前五名模型排名:\n")
                        for i, (model, score) in enumerate(selection_result['top_models'], 1):
                            result_text.insert(tk.END, f"  {i}. {model}: {score:.4f}\n")

                        result_text.insert(tk.END, f"\n推荐理由:\n")
                        result_text.insert(tk.END, selection_result['selection_reasoning'])

                        # 更新GUI中的模型选择
                        self.gui.viz_model_var.set(selection_result['best_model'])

                    else:
                        result_text.insert(tk.END, "❌ 模型选择失败，请检查训练结果")

                except Exception as e:
                    result_text.insert(tk.END, f"❌ 分析过程出错: {e}")

            ttk.Button(strategy_window, text="开始分析", command=run_selection).pack(pady=10)
            ttk.Button(strategy_window, text="关闭", command=strategy_window.destroy).pack(pady=5)

        except ImportError:
            messagebox.showerror("错误", "无法导入最佳模型选择模块")
        except Exception as e:
            messagebox.showerror("错误", f"自动选择最佳模型失败: {e}")

    def run_complete_analysis(self):
        """运行完整的二分类分析流程"""
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return

        try:
            from binary_classification_pipeline import run_binary_classification_analysis

            # 创建分析配置窗口
            config_window = tk.Toplevel(self.gui.root)
            config_window.title("完整分析配置")
            config_window.geometry("500x400")
            config_window.transient(self.gui.root)
            config_window.grab_set()

            # 配置选项
            ttk.Label(config_window, text="完整二分类分析配置", font=("Arial", 14, "bold")).pack(pady=10)

            # 模型选择策略
            strategy_frame = ttk.LabelFrame(config_window, text="模型选择策略")
            strategy_frame.pack(fill=tk.X, padx=10, pady=5)

            strategy_var = tk.StringVar(value="balanced")
            strategies = [
                ("balanced", "平衡策略"),
                ("performance", "性能优先"),
                ("robustness", "稳健性优先"),
                ("interpretability", "可解释性优先")
            ]

            for value, text in strategies:
                ttk.Radiobutton(strategy_frame, text=text, variable=strategy_var,
                               value=value).pack(anchor=tk.W, padx=10, pady=2)

            # 分析选项
            options_frame = ttk.LabelFrame(config_window, text="分析选项")
            options_frame.pack(fill=tk.X, padx=10, pady=5)

            enable_tuning_var = tk.BooleanVar(value=True)
            enable_shap_var = tk.BooleanVar(value=True)

            ttk.Checkbutton(options_frame, text="启用超参数调优",
                           variable=enable_tuning_var).pack(anchor=tk.W, padx=10, pady=2)
            ttk.Checkbutton(options_frame, text="启用SHAP可解释性分析",
                           variable=enable_shap_var).pack(anchor=tk.W, padx=10, pady=2)

            # 进度显示
            progress_frame = ttk.LabelFrame(config_window, text="分析进度")
            progress_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            progress_text = tk.Text(progress_frame, height=10)
            progress_scrollbar = ttk.Scrollbar(progress_frame, orient=tk.VERTICAL, command=progress_text.yview)
            progress_text.configure(yscrollcommand=progress_scrollbar.set)

            progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            progress_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            def run_analysis():
                strategy = strategy_var.get()
                enable_tuning = enable_tuning_var.get()
                enable_shap = enable_shap_var.get()

                progress_text.delete(1.0, tk.END)
                progress_text.insert(tk.END, "🚀 开始完整的二分类分析流程...\n")
                progress_text.insert(tk.END, f"数据文件: {self.gui.current_data_path.get()}\n")
                progress_text.insert(tk.END, f"选择的模型: {', '.join(selected_models)}\n")
                progress_text.insert(tk.END, f"选择策略: {strategy}\n")
                progress_text.insert(tk.END, f"超参数调优: {'启用' if enable_tuning else '禁用'}\n")
                progress_text.insert(tk.END, f"SHAP分析: {'启用' if enable_shap else '禁用'}\n")
                progress_text.insert(tk.END, "="*50 + "\n")
                config_window.update()

                try:
                    # 在新线程中运行分析
                    def analysis_thread():
                        success = run_binary_classification_analysis(
                            data_path=self.gui.current_data_path.get(),
                            selected_models=selected_models,
                            strategy=strategy,
                            enable_tuning=enable_tuning,
                            enable_shap=enable_shap
                        )

                        # 更新GUI
                        if success:
                            progress_text.insert(tk.END, "\n🎉 完整分析成功完成！\n")
                            progress_text.insert(tk.END, "请查看输出目录中的详细报告和可视化结果。\n")

                            # 自动加载最佳模型到可视化界面
                            try:
                                from best_model_selector import BestModelSelector
                                selector = BestModelSelector(strategy=strategy)
                                if selector.load_model_results():
                                    result = selector.select_best_model(top_k=1)
                                    if result:
                                        self.gui.viz_model_var.set(result['best_model'])
                                        progress_text.insert(tk.END, f"已自动选择最佳模型: {result['best_model']}\n")
                            except:
                                pass
                        else:
                            progress_text.insert(tk.END, "\n❌ 分析过程中出现错误！\n")

                    import threading
                    thread = threading.Thread(target=analysis_thread)
                    thread.daemon = True
                    thread.start()

                except Exception as e:
                    progress_text.insert(tk.END, f"\n❌ 启动分析失败: {e}\n")

            # 按钮
            button_frame = ttk.Frame(config_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="开始分析", command=run_analysis).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

        except ImportError:
            messagebox.showerror("错误", "无法导入完整分析流程模块")
        except Exception as e:
            messagebox.showerror("错误", f"启动完整分析失败: {e}")

    def start_hyperparameter_tuning(self):
        """开始超参数调优"""
        if self.is_training:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return

        # 检查数据是否已加载
        if not self.gui.current_data_path.get():
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        # 检查是否选择了模型
        selected_models = self.gui.get_selected_models()
        if not selected_models:
            messagebox.showwarning("警告", "请至少选择一个模型")
            return

        # 获取调优配置
        tuning_config = self.gui.get_tuning_config()
        if not tuning_config['enabled']:
            messagebox.showwarning("警告", "请先启用超参数调优")
            return

        # 在新线程中执行调优
        self.tuning_thread = threading.Thread(
            target=self._hyperparameter_tuning_thread,
            args=(selected_models, tuning_config)
        )
        self.tuning_thread.daemon = True
        self.tuning_thread.start()

    def _hyperparameter_tuning_thread(self, selected_models, tuning_config):
        """在后台线程中执行超参数调优"""
        try:
            self.is_training = True
            self.gui.status_text.set("正在进行超参数调优...")
            self.gui.log_message("开始超参数调优")

            # 导入调优模块
            try:
                from code.hyperparameter_tuning import HyperparameterTuner, tune_model
            except ImportError:
                self.gui.log_message("无法导入超参数调优模块")
                messagebox.showerror("错误", "无法导入超参数调优模块")
                return

            # 准备数据
            data_path = self.gui.current_data_path.get()
            test_size = self.gui.test_size_var.get()
            random_seed = self.gui.random_seed_var.get()
            scaling_method = self.gui.scaling_var.get()

            # 创建数据预处理器
            preprocessor = DataPreprocessor(
                test_size=test_size,
                random_state=random_seed,
                scaling_method=scaling_method if scaling_method != "none" else None
            )

            # 加载和预处理数据
            X_train, X_test, y_train, y_test = preprocessor.load_and_preprocess(data_path)

            # 存储调优结果
            self.tuning_results = {}

            # 对每个选中的模型进行调优
            total_models = len(selected_models)
            for i, model_name in enumerate(selected_models):
                if not self.is_training:  # 检查是否被停止
                    break

                self.gui.log_message(f"正在调优 {model_name}...")
                progress = (i / total_models) * 100
                self.gui.training_progress.set(progress)

                try:
                    # 执行超参数调优，获取study对象用于可视化
                    # 检查数据集类型
                    dataset_type = None
                    if hasattr(self, 'dataset_info') and self.dataset_info:
                        # 如果有数据集信息，尝试确定数据集类型
                        n_samples = len(X_train)
                        n_features = X_train.shape[1]
                        
                        if n_samples < 1000:
                            dataset_type = 'small'
                        elif n_samples > 10000:
                            dataset_type = 'large'
                        elif n_features > 100:
                            dataset_type = 'high_dim'
                        
                        # 检查是否有类别不平衡
                        from collections import Counter
                        class_counts = Counter(y_train)
                        if len(class_counts) >= 2:
                            most_common = class_counts.most_common(1)[0][1]
                            least_common = min(class_counts.values())
                            if most_common / least_common > 3:  # 3:1的不平衡比例
                                # 不平衡数据集优先级更高
                                dataset_type = 'imbalanced'
                    
                    # 确定调优模式
                    tuning_mode = 'standard'
                    if tuning_config.get('quick_mode', False):
                        tuning_mode = 'quick'
                    elif tuning_config.get('deep_mode', False):
                        tuning_mode = 'deep'
                    
                    self.gui.log_message(f"开始对 {model_name} 进行超参数调优，数据集类型: {dataset_type}，调优模式: {tuning_mode}")
                    
                    try:
                        # 调用新的tune_model函数
                        best_params, best_score, study = tune_model(
                            model_name=model_name,
                            X_train=X_train,
                            y_train=y_train,
                            n_trials=tuning_config.get('n_trials'),
                            dataset_type=dataset_type,
                            tuning_mode=tuning_mode,
                            return_study=True
                        )
                    except NameError as e:
                        self.gui.log_message(f"名称错误: {e}，检查是否正确导入tune_model函数")
                        raise
                    except ImportError as e:
                        self.gui.log_message(f"导入错误: {e}，检查模块路径是否正确")
                        raise
                    except Exception as e:
                        self.gui.log_message(f"调用tune_model时出错: {e}")
                        raise

                    # 保存调优结果
                    self.tuning_results[model_name] = {
                        'best_params': best_params,
                        'best_score': best_score,
                        'config': tuning_config,
                        'study': study,
                        'X_train': X_train,
                        'y_train': y_train
                    }

                    self.gui.log_message(f"{model_name} 调优完成，最佳得分: {best_score:.4f}")

                except Exception as e:
                    self.gui.log_message(f"{model_name} 调优失败: {e}")

            # 调优完成
            self.gui.training_progress.set(100)
            self.gui.status_text.set(f"超参数调优完成，共调优 {len(self.tuning_results)} 个模型")
            self.gui.log_message("所有模型超参数调优完成")

            # 显示调优结果摘要
            self._show_tuning_summary()

        except Exception as e:
            self.gui.log_message(f"超参数调优过程出错: {e}")
            messagebox.showerror("错误", f"超参数调优失败: {e}")
        finally:
            self.is_training = False

    def _show_tuning_summary(self):
        """显示调优结果摘要"""
        if not hasattr(self, 'tuning_results') or not self.tuning_results:
            return

        summary_text = "\n🎯 超参数调优结果摘要:\n" + "="*60 + "\n"

        # 按得分排序
        sorted_results = sorted(
            self.tuning_results.items(),
            key=lambda x: x[1]['best_score'],
            reverse=True
        )

        # 计算改进情况（如果有基准分数的话）
        best_model = sorted_results[0][0]
        best_score = sorted_results[0][1]['best_score']

        summary_text += f"\n🏆 最佳模型: {best_model} (得分: {best_score:.4f})\n"
        summary_text += "\n📊 所有模型调优结果:\n"

        for i, (model_name, result) in enumerate(sorted_results, 1):
            score = result['best_score']
            params = result['best_params']

            # 简化参数显示
            param_summary = []
            for key, value in params.items():
                if isinstance(value, float):
                    param_summary.append(f"{key}={value:.3f}")
                else:
                    param_summary.append(f"{key}={value}")

            param_str = ", ".join(param_summary[:3])  # 只显示前3个参数
            if len(params) > 3:
                param_str += f" (共{len(params)}个参数)"

            summary_text += f"\n{i:2d}. {model_name:15s} | 得分: {score:.4f} | 主要参数: {param_str}\n"

        summary_text += "\n" + "="*60
        summary_text += "\n💡 提示: 点击'📊 调优结果'按钮查看详细的可视化结果"

        self.gui.log_message(summary_text)

        # 同时在训练日志中显示
        self.gui.training_log.insert(tk.END, summary_text + "\n")
        self.gui.training_log.see(tk.END)

    def show_tuning_results(self):
        """显示详细的调优结果"""
        if not hasattr(self, 'tuning_results') or not self.tuning_results:
            messagebox.showwarning("警告", "没有可显示的调优结果，请先进行超参数调优")
            return

        # 创建结果显示窗口
        result_window = tk.Toplevel(self.gui.root)
        result_window.title("超参数调优结果")
        result_window.geometry("800x600")
        result_window.transient(self.gui.root)
        result_window.grab_set()

        # 创建选项卡
        notebook = ttk.Notebook(result_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 结果摘要选项卡
        self._create_summary_tab(notebook)

        # 详细结果选项卡
        self._create_detailed_results_tab(notebook)

        # 参数比较选项卡
        self._create_parameter_comparison_tab(notebook)

        # 可视化选项卡
        self._create_visualization_tab(notebook)

    def _create_summary_tab(self, notebook):
        """创建结果摘要选项卡"""
        summary_frame = ttk.Frame(notebook)
        notebook.add(summary_frame, text="结果摘要")

        # 创建表格显示结果
        columns = ("排名", "模型", "最佳得分", "改进幅度")
        tree = ttk.Treeview(summary_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 填充数据
        sorted_results = sorted(
            self.tuning_results.items(),
            key=lambda x: x[1]['best_score'],
            reverse=True
        )

        baseline_scores = {}  # 这里可以存储基线模型得分进行比较

        for i, (model_name, result) in enumerate(sorted_results, 1):
            best_score = result['best_score']
            # 计算改进幅度（这里简化处理，实际应该与基线模型比较）
            improvement = "N/A"  # 可以后续完善

            tree.insert("", "end", values=(
                i, model_name, f"{best_score:.4f}", improvement
            ))

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_detailed_results_tab(self, notebook):
        """创建详细结果选项卡"""
        detail_frame = ttk.Frame(notebook)
        notebook.add(detail_frame, text="详细结果")

        # 模型选择
        select_frame = ttk.Frame(detail_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(select_frame, text="选择模型:").pack(side=tk.LEFT, padx=5)
        model_var = tk.StringVar()
        model_combo = ttk.Combobox(select_frame, textvariable=model_var,
                                 values=list(self.tuning_results.keys()), state="readonly")
        model_combo.pack(side=tk.LEFT, padx=5)

        # 详细信息显示
        detail_text = tk.Text(detail_frame, wrap=tk.WORD)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=detail_text.yview)
        detail_text.configure(yscrollcommand=detail_scrollbar.set)

        def on_model_select(event):
            selected_model = model_var.get()
            if selected_model and selected_model in self.tuning_results:
                result = self.tuning_results[selected_model]

                detail_info = f"模型: {selected_model}\n"
                detail_info += f"最佳得分: {result['best_score']:.4f}\n\n"
                detail_info += "最佳参数:\n"
                for param, value in result['best_params'].items():
                    detail_info += f"  {param}: {value}\n"

                detail_info += f"\n调优配置:\n"
                config = result['config']
                detail_info += f"  试验次数: {config['n_trials']}\n"
                detail_info += f"  调优策略: {config['strategy']}\n"
                detail_info += f"  评估指标: {config['metric']}\n"

                detail_text.delete(1.0, tk.END)
                detail_text.insert(1.0, detail_info)

        model_combo.bind("<<ComboboxSelected>>", on_model_select)

        detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 默认选择第一个模型
        if self.tuning_results:
            model_combo.set(list(self.tuning_results.keys())[0])
            on_model_select(None)

    def _create_parameter_comparison_tab(self, notebook):
        """创建参数比较选项卡"""
        comparison_frame = ttk.Frame(notebook)
        notebook.add(comparison_frame, text="参数比较")

        # 创建参数比较表格
        comparison_text = tk.Text(comparison_frame, wrap=tk.WORD)
        comparison_scrollbar = ttk.Scrollbar(comparison_frame, orient=tk.VERTICAL, command=comparison_text.yview)
        comparison_text.configure(yscrollcommand=comparison_scrollbar.set)

        # 生成参数比较信息
        comparison_info = "模型参数比较:\n" + "="*60 + "\n\n"

        for model_name, result in self.tuning_results.items():
            comparison_info += f"{model_name} (得分: {result['best_score']:.4f}):\n"
            for param, value in result['best_params'].items():
                comparison_info += f"  {param}: {value}\n"
            comparison_info += "\n"

        comparison_text.insert(1.0, comparison_info)
        comparison_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        comparison_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_visualization_tab(self, notebook):
        """创建可视化选项卡"""
        viz_frame = ttk.Frame(notebook)
        notebook.add(viz_frame, text="可视化")

        # 控制面板
        control_frame = ttk.Frame(viz_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 模型选择
        ttk.Label(control_frame, text="选择模型:").pack(side=tk.LEFT, padx=5)
        viz_model_var = tk.StringVar()
        viz_model_combo = ttk.Combobox(control_frame, textvariable=viz_model_var,
                                     values=list(self.tuning_results.keys()), state="readonly")
        viz_model_combo.pack(side=tk.LEFT, padx=5)

        # 图表类型选择
        ttk.Label(control_frame, text="图表类型:").pack(side=tk.LEFT, padx=(20, 5))
        chart_type_var = tk.StringVar(value="优化历史")
        chart_combo = ttk.Combobox(control_frame, textvariable=chart_type_var,
                                 values=["优化历史", "参数重要性", "参数关系"], state="readonly")
        chart_combo.pack(side=tk.LEFT, padx=5)

        # 生成图表按钮
        ttk.Button(control_frame, text="生成图表",
                  command=lambda: self._generate_tuning_chart(viz_model_var.get(), chart_type_var.get(), chart_frame)).pack(side=tk.LEFT, padx=10)
        
        # 添加保存按钮
        ttk.Button(control_frame, text="保存图表为PDF",
                  command=lambda: self._save_tuning_chart_to_pdf(viz_model_var.get(), chart_type_var.get())).pack(side=tk.LEFT, padx=10)

        # 图表显示区域
        chart_frame = ttk.Frame(viz_frame)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 默认选择第一个模型
        if self.tuning_results:
            viz_model_combo.set(list(self.tuning_results.keys())[0])
            
    def _save_tuning_chart_to_pdf(self, model_name, chart_type):
        """将调优图表保存为PDF"""
        if not model_name or model_name not in self.tuning_results:
            messagebox.showwarning("警告", "请选择有效的模型")
            return
            
        result = self.tuning_results[model_name]
        study = result.get('study')
        
        if study is None:
            messagebox.showwarning("警告", "该模型没有可用的调优数据")
            return
            
        try:
            # 导入调优模块
            from code.hyperparameter_tuning import HyperparameterTuner
            
            # 创建调优器实例
            tuner = HyperparameterTuner(model_name=model_name)
            
            # 确定可视化类型
            viz_type = 'optimization_history'
            if chart_type == "参数重要性":
                viz_type = 'param_importances'
            elif chart_type == "参数关系":
                viz_type = 'slice'
                
            # 显示保存进度对话框
            progress_window = tk.Toplevel(self.gui.root)
            progress_window.title("保存图表")
            progress_window.geometry("300x100")
            progress_window.transient(self.gui.root)
            progress_window.grab_set()
            
            ttk.Label(progress_window, text="正在准备保存图表...").pack(pady=10)
            progress = ttk.Progressbar(progress_window, mode="indeterminate")
            progress.pack(fill=tk.X, padx=20, pady=10)
            progress.start()
            
            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("HTML files", "*.html"), ("All files", "*.*")]
            )
            
            if not file_path:
                progress_window.destroy()
                return
                
            # 更新进度窗口
            ttk.Label(progress_window, text=f"正在保存为 {os.path.basename(file_path)}...").pack(pady=5)
            
            def save_chart_thread():
                try:
                    # 保存图表
                    saved_path = tuner.save_tuning_visualization_to_pdf(study, viz_type, file_path)
                    
                    # 在主线程中更新UI
                    self.gui.root.after(0, lambda: self._show_save_result(saved_path, progress_window))
                except Exception as e:
                    self.gui.root.after(0, lambda: self._show_save_error(str(e), progress_window))
            
            # 在后台线程中执行保存操作
            import threading
            save_thread = threading.Thread(target=save_chart_thread)
            save_thread.daemon = True
            save_thread.start()
                
        except ImportError:
            messagebox.showerror("错误", "无法导入超参数调优模块")
        except Exception as e:
            messagebox.showerror("错误", f"保存图表失败: {e}")
            
    def _show_save_result(self, saved_path, progress_window):
        """显示保存结果"""
        progress_window.destroy()
        
        if saved_path:
            result = messagebox.askyesno("成功", f"图表已保存到: {saved_path}\n\n是否打开保存的文件?")
            if result:
                try:
                    import os
                    import platform
                    import subprocess
                    
                    if platform.system() == 'Windows':
                        os.startfile(saved_path)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.run(['open', saved_path])
                    else:  # Linux
                        subprocess.run(['xdg-open', saved_path])
                except Exception as e:
                    messagebox.showwarning("警告", f"无法打开文件: {e}")
        else:
            messagebox.showerror("错误", "保存图表失败")
            
    def _show_save_error(self, error_msg, progress_window):
        """显示保存错误"""
        progress_window.destroy()
        messagebox.showerror("错误", f"保存图表失败: {error_msg}")
            
    def _generate_tuning_chart(self, model_name, chart_type, parent_frame):
        """生成调优图表"""
        if not model_name or model_name not in self.tuning_results:
            messagebox.showwarning("警告", "请选择有效的模型")
            return

        result = self.tuning_results[model_name]
        study = result.get('study')

        if study is None:
            messagebox.showwarning("警告", "该模型没有可用的调优数据")
            return

        # 清除之前的图表
        for widget in parent_frame.winfo_children():
            widget.destroy()

        try:
            # 创建matplotlib图形
            fig, ax = plt.subplots(figsize=(10, 6))

            if chart_type == "优化历史":
                self._plot_optimization_history(ax, study, model_name)
            elif chart_type == "参数重要性":
                self._plot_parameter_importance(ax, study, model_name)
            elif chart_type == "参数关系":
                self._plot_parameter_relationships(ax, study, model_name)

            # 在GUI中显示图表
            canvas = FigureCanvasTkAgg(fig, parent_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
            toolbar = NavigationToolbar2Tk(canvas, parent_frame)
            toolbar.update()

        except Exception as e:
            messagebox.showerror("错误", f"生成图表失败: {e}")

    def _plot_optimization_history(self, ax, study, model_name):
        """绘制优化历史"""
        trials = study.trials
        if not trials:
            ax.text(0.5, 0.5, "没有可用的试验数据", ha='center', va='center', transform=ax.transAxes)
            return

        # 提取试验数据
        trial_numbers = [trial.number for trial in trials]
        values = [trial.value if trial.value is not None else 0 for trial in trials]

        # 计算累积最佳值
        best_values = []
        current_best = float('-inf')
        for value in values:
            if value > current_best:
                current_best = value
            best_values.append(current_best)

        # 绘制图表
        ax.plot(trial_numbers, values, 'o-', alpha=0.6, label='试验值')
        ax.plot(trial_numbers, best_values, 'r-', linewidth=2, label='最佳值')

        ax.set_xlabel('试验次数')
        ax.set_ylabel('目标值')
        ax.set_title(f'{model_name} - 优化历史')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_parameter_importance(self, ax, study, model_name):
        """绘制参数重要性"""
        try:
            # 计算参数重要性
            importance = optuna.importance.get_param_importances(study)

            if not importance:
                ax.text(0.5, 0.5, "无法计算参数重要性", ha='center', va='center', transform=ax.transAxes)
                return

            # 准备数据
            params = list(importance.keys())
            importances = list(importance.values())

            # 创建水平条形图
            y_pos = np.arange(len(params))
            ax.barh(y_pos, importances)
            ax.set_yticks(y_pos)
            ax.set_yticklabels(params)
            ax.set_xlabel('重要性')
            ax.set_title(f'{model_name} - 参数重要性')
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f"计算参数重要性失败: {e}", ha='center', va='center', transform=ax.transAxes)

    def _plot_parameter_relationships(self, ax, study, model_name):
        """绘制参数关系"""
        trials = study.trials
        if len(trials) < 2:
            ax.text(0.5, 0.5, "试验数据不足", ha='center', va='center', transform=ax.transAxes)
            return

        # 获取所有参数名
        all_params = set()
        for trial in trials:
            all_params.update(trial.params.keys())

        if len(all_params) < 2:
            ax.text(0.5, 0.5, "参数数量不足", ha='center', va='center', transform=ax.transAxes)
            return

        # 选择前两个参数进行可视化
        param_names = list(all_params)[:2]
        param1_name, param2_name = param_names[0], param_names[1]

        # 提取参数值和目标值
        param1_values = []
        param2_values = []
        objective_values = []

        for trial in trials:
            if param1_name in trial.params and param2_name in trial.params and trial.value is not None:
                param1_values.append(trial.params[param1_name])
                param2_values.append(trial.params[param2_name])
                objective_values.append(trial.value)

        if not param1_values:
            ax.text(0.5, 0.5, "没有有效的参数数据", ha='center', va='center', transform=ax.transAxes)
            return

        # 创建散点图
        scatter = ax.scatter(param1_values, param2_values, c=objective_values, cmap='viridis', alpha=0.6)
        ax.set_xlabel(param1_name)
        ax.set_ylabel(param2_name)
        ax.set_title(f'{model_name} - 参数关系 ({param1_name} vs {param2_name})')

        # 添加颜色条
        plt.colorbar(scatter, ax=ax, label='目标值')

    def generate_performance_report(self):
        """生成模型性能比较报告"""
        try:
            # 检查是否有训练好的模型
            from config import CACHE_PATH
            from pathlib import Path

            cache_path = Path(CACHE_PATH)
            if not cache_path.exists():
                messagebox.showwarning("警告", "没有找到模型缓存，请先训练模型")
                return

            # 查找已训练的模型
            model_files = list(cache_path.glob("*_results.joblib"))
            if not model_files:
                messagebox.showwarning("警告", "没有找到已训练的模型，请先训练模型")
                return

            # 创建进度窗口
            progress_window = tk.Toplevel(self.gui.root)
            progress_window.title("生成性能报告")
            progress_window.geometry("600x400")
            progress_window.transient(self.gui.root)
            progress_window.grab_set()

            # 进度显示
            ttk.Label(progress_window, text="正在生成模型性能比较报告...",
                     font=('Arial', 12, 'bold')).pack(pady=10)

            progress_text = tk.Text(progress_window, height=20, width=70)
            progress_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            scrollbar = ttk.Scrollbar(progress_window, orient="vertical", command=progress_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            progress_text.configure(yscrollcommand=scrollbar.set)

            def generate_report_thread():
                try:
                    progress_text.insert(tk.END, "🚀 开始生成模型性能比较报告...\n")
                    progress_text.update()

                    # 导入报告生成模块
                    from model_performance_report import generate_comprehensive_report

                    progress_text.insert(tk.END, "📊 正在加载模型结果...\n")
                    progress_text.update()

                    # 生成报告
                    generate_comprehensive_report()

                    progress_text.insert(tk.END, "\n✅ 报告生成完成！\n")
                    progress_text.insert(tk.END, "📁 报告文件位置：\n")
                    progress_text.insert(tk.END, "   - HTML报告: reports/performance_report.html\n")
                    progress_text.insert(tk.END, "   - CSV数据: reports/performance_metrics.csv\n")
                    progress_text.insert(tk.END, "   - JSON数据: reports/performance_data.json\n")
                    progress_text.insert(tk.END, "   - 性能热图: reports/performance_heatmap.png\n")
                    progress_text.insert(tk.END, "   - 雷达对比图: reports/radar_comparison.png\n")
                    progress_text.insert(tk.END, "   - 模型排名图: reports/model_ranking.png\n")
                    progress_text.insert(tk.END, "\n💡 您可以打开HTML报告查看详细结果\n")

                    # 添加打开报告按钮
                    def open_html_report():
                        try:
                            import webbrowser
                            from config import PROJECT_ROOT
                            report_path = PROJECT_ROOT / 'reports' / 'performance_report.html'
                            webbrowser.open(f'file:///{report_path}')
                        except Exception as e:
                            messagebox.showerror("错误", f"打开报告失败: {e}")

                    button_frame = ttk.Frame(progress_window)
                    button_frame.pack(fill=tk.X, padx=10, pady=5)

                    ttk.Button(button_frame, text="打开HTML报告",
                              command=open_html_report).pack(side=tk.LEFT, padx=5)
                    ttk.Button(button_frame, text="关闭",
                              command=progress_window.destroy).pack(side=tk.RIGHT, padx=5)

                except Exception as e:
                    progress_text.insert(tk.END, f"\n❌ 报告生成失败: {e}\n")
                    progress_text.insert(tk.END, "请确保已经训练了模型并且缓存文件存在\n")

            # 在新线程中生成报告
            report_thread = threading.Thread(target=generate_report_thread)
            report_thread.daemon = True
            report_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动报告生成失败: {e}")

class ShapExplainer:
    """SHAP解释器类，用于生成SHAP可视化"""
    
    def __init__(self, gui):
        self.gui = gui
        # 定义保存格式为PDF
        self.save_format = 'pdf'
    
    def generate_shap_plots(self, model, X_test, feature_names, output_dir, model_name):
        """
// ... existing code ...
            plt.title(f'{model_name} Model - SHAP Summary Plot', fontsize=14, pad=20)
            plt.tight_layout()
            summary_path = output_dir / f'{model_name}_shap_summary.{self.save_format}'
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"SHAP summary plot saved: {summary_path}")

            # 2. Decision Plot (current command-line style)
// ... existing code ...
            plt.title('Decision Plot (Top 50 Samples)')
            plt.tight_layout()
            decision_path = output_dir / f"{model_name}_decision_plot.{self.save_format}"
            plt.savefig(decision_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.gui.log_message(f"Decision plot saved: {decision_path}")

            # 3. Waterfall Plots (current command-line style)
// ... existing code ...
                plt.title(f'Waterfall Plot (Sample {idx})')
                plt.tight_layout()
                waterfall_path = output_dir / f"{model_name}_waterfall_{idx}.{self.save_format}"
                plt.savefig(waterfall_path, dpi=300, bbox_inches='tight')
                plt.close()
                self.gui.log_message(f"Waterfall plot saved: {waterfall_path}")
// ... existing code ...
        """
        pass
