#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, recall_score, f1_score, roc_curve, auc, ConfusionMatrixDisplay, confusion_matrix, precision_score
from sklearn.calibration import calibration_curve
from sklearn.preprocessing import label_binarize
import numpy as np
from joblib import load, dump
from pathlib import Path
import os
from sklearn.model_selection import learning_curve

# 尝试导入配置模块和绘图工具
try:
    from config import OUTPUT_PATH, CACHE_PATH, MODEL_DISPLAY_NAMES, PLOT_CONFIG
    from plot_utils import translate_term, get_save_path, save_plot as utils_save_plot
except ImportError:
    # 通过获取当前文件的路径确定项目根目录
    PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    OUTPUT_PATH = PROJECT_ROOT / 'output'
    MODEL_DISPLAY_NAMES = {
        'DecisionTree': 'Decision Tree',
        'RandomForest': 'Random Forest',
        'XGBoost': 'XGBoost',
        'LightGBM': 'LightGBM',
        'CatBoost': 'CatBoost',
        'Logistic': 'Logistic Regression',
        'SVM': 'SVM',
        'KNN': 'KNN',
        'NaiveBayes': 'Naive Bayes',
        'NeuralNet': 'Neural Network'
    }
    PLOT_CONFIG = {
        'dpi': 150,
        'figsize': (10, 8),
        'save_format': 'pdf'
    }

# 尝试导入日志模块
try:
    from logger import get_default_logger
    logger = get_default_logger("plot_single_model")
except ImportError:
    import logging
    logger = logging.getLogger("plot_single_model")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 修改绘图配置
CONFIG = {
    'output_path': Path(OUTPUT_PATH),
    'dpi': 150,
    'figsize': (10, 7),  # 稍微宽一些，更美观
    'fontsize': {
        'title': 16,       # 标题字体大小
        'subtitle': 14,    # 副标题字体大小
        'label': 12,       # 轴标签字体大小
        'tick': 10,        # 刻度标签字体大小
        'legend': 10,      # 图例字体大小
        'annotation': 10   # 注释字体大小
    },
    'colors': {
        'primary': '#1f77b4',   # 蓝色
        'secondary': '#ff7f0e', # 橙色
        'tertiary': '#2ca02c',  # 绿色
        'highlight': '#d62728', # 红色
        'grid': '#cccccc',      # 浅灰色
        'background': '#f8f9fa' # 非常浅的灰色背景
    },
    'marker_size': 8,
    'line_width': 2,
    'grid_alpha': 0.3
}

# 配置matplotlib - 从plot_utils导入
try:
    from plot_utils import get_font_properties
except ImportError:
    # 如果无法导入，提供一个简单的实现
    def get_font_properties():
        import platform
        try:
            from matplotlib.font_manager import FontProperties
            if platform.system() == 'Windows':
                try:
                    return FontProperties(fname=r"C:\Windows\Fonts\msyh.ttc")  # 微软雅黑
                except:
                    try:
                        return FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")  # 黑体
                    except:
                        return FontProperties(family=['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif'])
            else:
                return FontProperties(family=['SimHei', 'DejaVu Sans', 'sans-serif'])
        except Exception as e:
            logger.warning(f"获取字体属性失败: {e}")
            return None

# 英文术语映射
TERM_MAPPING = {
    '假阳性率': 'False Positive Rate (FPR)',
    '真阳性率': 'True Positive Rate (TPR)',
    'ROC曲线': 'ROC Curve',
    '混淆矩阵': 'Confusion Matrix',
    '预测类别': 'Predicted Class',
    '真实类别': 'True Class',
    '决策曲线分析': 'Decision Curve Analysis',
    '阈值概率': 'Threshold Probability',
    '净收益': 'Net Benefit',
    '临床影响曲线': 'Clinical Impact Curve',
    '风险阈值': 'Risk Threshold',
    '高风险比例': 'High-risk Proportion',
    '校准曲线': 'Calibration Curve',
    '预测概率': 'Predicted Probability',
    '真实概率': 'Actual Probability',
    '完美校准': 'Perfect Calibration',
    'SHAP摘要图': 'SHAP Summary Plot',
    'SHAP依赖图': 'SHAP Dependence Plot',
    '决策图': 'Decision Plot',
    '瀑布图': 'Waterfall Plot',
    '模型性能雷达图': 'Model Performance Radar Chart',
    '样本': 'Sample',
    '准确率': 'Accuracy',
    '精确率': 'Precision',
    '召回率': 'Recall',
    '特异性': 'Specificity',
    'F1分数': 'F1 Score'
}

def translate(term):
    """将中文术语翻译为英文"""
    return TERM_MAPPING.get(term, term)

def save_plot(fig, filename, model_name, plot_type=None):
    """
    保存图表到特定模型的目录
    
    Args:
        fig: 图形对象
        filename: 文件名
        model_name: 模型名称
        plot_type: 绘图类型
    """
    try:
        # 确保目标目录存在
        model_dir = CONFIG['output_path'] / model_name
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)

        # 指定完整的保存路径
        filepath = model_dir / filename
        
        # 检查是否为PDF文件
        is_pdf = str(filepath).lower().endswith('.pdf')
        
        if is_pdf:
            try:
                # 尝试直接保存为PDF
                fig.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight', format='pdf')
                logger.info(f"图表已保存为PDF: {filepath}")
            except Exception as pdf_error:
                logger.warning(f"保存为PDF失败: {pdf_error}，尝试备选方案")
                
                try:
                    # 创建新的图形并复制内容
                    new_fig = plt.figure(figsize=fig.get_size_inches())
                    for i, ax_old in enumerate(fig.get_axes()):
                        # 复制轴到新图形
                        ax_new = new_fig.add_subplot(len(fig.get_axes()), 1, i+1)
                        
                        # 复制线条
                        for line in ax_old.get_lines():
                            ax_new.plot(line.get_xdata(), line.get_ydata(), 
                                      color=line.get_color(), 
                                      linestyle=line.get_linestyle(),
                                      marker=line.get_marker(),
                                      label=line.get_label())
                        
                        # 复制标题和标签
                        ax_new.set_title(ax_old.get_title())
                        ax_new.set_xlabel(ax_old.get_xlabel())
                        ax_new.set_ylabel(ax_old.get_ylabel())
                        
                        # 复制图例
                        if ax_old.get_legend():
                            ax_new.legend()
                            
                        # 复制网格设置
                        ax_new.grid(ax_old.get_grid())
                    
                    # 调整布局
                    new_fig.tight_layout()
                    
                    # 保存新图形
                    new_fig.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
                    plt.close(new_fig)
                    logger.info(f"使用备选方案保存图表成功: {filepath}")
                except Exception as alt_error:
                    # 如果备选方案也失败，尝试保存为PNG
                    logger.warning(f"备选方案失败: {alt_error}，尝试保存为PNG")
                    png_path = str(filepath).replace('.pdf', '.png')
                    fig.savefig(png_path, dpi=CONFIG['dpi'], bbox_inches='tight', format='png')
                    logger.info(f"图表已保存为PNG: {png_path}")
        else:
            # 非PDF格式直接保存
            fig.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
            logger.info(f"图表已保存至: {filepath}")
        
        plt.close(fig)
    except Exception as e:
        logger.error(f"保存图表 {filename} 失败: {e}")
        plt.close(fig)

def plot_curve(ax, x, y, label, title, xlabel, ylabel, color=None, linestyle='-'):
    ax.plot(x, y, label=label, color=color, linestyle=linestyle)
    ax.set_title(translate(title))
    ax.set_xlabel(translate(xlabel))
    ax.set_ylabel(translate(ylabel))
    ax.legend(loc='best')

def calculate_dca(y_true, y_prob, thresholds):
    net_benefit = []
    for thresh in thresholds:
        y_pred = (y_prob >= thresh).astype(int)
        tp = np.sum((y_true == 1) & (y_pred == 1))
        fp = np.sum((y_true == 0) & (y_pred == 1))
        nb = (tp / len(y_true)) - (fp / len(y_true)) * (thresh / (1 - thresh))
        net_benefit.append(nb)
    return np.array(net_benefit)

def calculate_cic(y_prob, thresholds):
    cic_values = []
    for thresh in thresholds:
        high_risk = np.mean(y_prob >= thresh)
        cic_values.append(high_risk)
    return np.array(cic_values)

def plot_model_visualizations(model_name):
    """
    为指定模型生成并保存所有可视化图表
    """
    logger.info(f"开始为模型 {model_name} 生成可视化图表...")
    
    # 获取模型的显示名称
    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    CONFIG['model_name'] = display_name
    
    # 加载模型和数据
    model_data = load_model(model_name)
    if not model_data:
        return
        
    model = model_data['model']
    X_test = model_data['X_test']
    y_test = model_data['y_test']
    feature_names = model_data.get('feature_names', list(X_test.columns) if hasattr(X_test, 'columns') else None)
    
    logger.info(f"模型和数据加载成功. X_test shape: {X_test.shape}, y_test shape: {y_test.shape}")
    
    # 确保保存格式为pdf
    save_format = CONFIG.get('save_format', 'pdf')
    
    # 1. 绘制ROC曲线 (调用新的美化函数)
    logger.info("生成ROC曲线...")
    plot_roc_curve(model, X_test, y_test, filename=f"{model_name}_roc.{save_format}")
    
    # 2. 绘制学习曲线 (调用新的美化函数)
    logger.info("生成学习曲线...")
    plot_learning_curve(model, X_test, y_test, filename=f"{model_name}_learning_curve.{save_format}")

    # 3. 绘制SHAP摘要图
    logger.info("生成SHAP摘要图...")
    plot_shap_summary(model, X_test, feature_names, model_name)
    
    # 4. 绘制SHAP依赖图 (示例：选择第一个特征)
    if feature_names and len(feature_names) > 0:
        logger.info(f"生成SHAP依赖图，特征: {feature_names[0]}")
        plot_shap_dependence(model, X_test, feature_names[0], model_name)
    else:
        logger.warning("没有可用的特征名称，跳过SHAP依赖图")

    logger.info(f"模型 {model_name} 的所有图表已生成完毕。")


def plot_shap_summary(model, X, feature_names=None, model_name='model', max_display=20, plot_type='bar', output_dir=None):
    """
    生成SHAP摘要图
    
    Args:
        model: 训练好的模型
        X: 特征数据
        feature_names: 特征名称列表，如果为None且X是DataFrame则使用X的列名
        model_name: 模型名称，用于保存文件
        max_display: 显示的最大特征数量
        plot_type: 图表类型，'bar'或'dot'
        output_dir: 输出目录，如果为None则使用默认目录
    
    Returns:
        str: 保存的文件路径
    """
    logger.info(f"为模型 {model_name} 生成SHAP摘要图")
    
    # 确保X是DataFrame格式，如果是numpy数组则转换
    if isinstance(X, np.ndarray):
        import pandas as pd
        # 如果提供了特征名称，使用它们
        if feature_names is not None:
            # 确保特征名称数量与特征数量匹配
            if len(feature_names) != X.shape[1]:
                logger.warning(f"特征名称数量 ({len(feature_names)}) 与特征数量 ({X.shape[1]}) 不匹配，使用默认特征编号")
                feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        else:
            # 如果没有提供特征名称，使用默认的特征编号
            feature_names = [f'feature_{i}' for i in range(X.shape[1])]
            
        X = pd.DataFrame(X, columns=feature_names)
    
    try:
        # 尝试使用TreeExplainer
        try:
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X)
            logger.info(f"TreeExplainer成功，shap_values类型: {type(shap_values)}, 形状: {np.array(shap_values).shape if isinstance(shap_values, list) else shap_values.shape}")
        except Exception as e:
            logger.warning(f"TreeExplainer 失败: {e}, 回退到 KernelExplainer")
            # 使用较小的背景数据集以提高效率
            background_size = min(100, len(X))
            background_data = X.sample(n=background_size, random_state=42) if hasattr(X, 'sample') else X[np.random.choice(X.shape[0], background_size, replace=False)]
            
            # 确保模型有predict_proba方法
            if hasattr(model, 'predict_proba'):
                predict_func = model.predict_proba
            else:
                predict_func = model.predict
                
            explainer = shap.KernelExplainer(predict_func, background_data)
            
            # 如果数据集太大，使用子集计算SHAP值
            sample_size = min(200, len(X))
            X_sample = X.sample(n=sample_size, random_state=42) if hasattr(X, 'sample') else X[np.random.choice(X.shape[0], sample_size, replace=False)]
            shap_values = explainer.shap_values(X_sample)
            X = X_sample  # 更新X为采样数据
            logger.info(f"KernelExplainer成功，shap_values类型: {type(shap_values)}, 形状: {np.array(shap_values).shape if isinstance(shap_values, list) else shap_values.shape}")
        
        # 创建新的图形并绘制SHAP摘要图
        plt.figure(figsize=(12, 8))
        
        # 处理不同类型的shap_values
        if isinstance(shap_values, list):
            # 二分类问题，通常我们关注正类
            logger.info(f"shap_values是列表，长度: {len(shap_values)}")
            if len(shap_values) > 1:
                # 二分类情况，使用正类的SHAP值
                shap_values_to_plot = shap_values[1]
                logger.info(f"使用正类(索引1)的SHAP值，形状: {shap_values_to_plot.shape}")
            else:
                shap_values_to_plot = shap_values[0]
                logger.info(f"使用索引0的SHAP值，形状: {shap_values_to_plot.shape}")
        elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
            # 三维数组，可能是多分类问题
            logger.info(f"shap_values是三维数组，形状: {shap_values.shape}")
            shap_values_to_plot = shap_values[:, :, 1] if shap_values.shape[2] > 1 else shap_values[:, :, 0]
        else:
            # 其他情况，直接使用
            shap_values_to_plot = shap_values
            logger.info(f"直接使用shap_values，形状: {shap_values_to_plot.shape}")
        
        # 确保特征名称与数据维度匹配
        if hasattr(X, 'columns'):
            feature_names_to_use = X.columns.tolist()
        else:
            feature_names_to_use = feature_names
            
        logger.info(f"使用特征名称: {feature_names_to_use[:5]}... (共{len(feature_names_to_use)}个)")
        
        # 绘制SHAP摘要图
        if plot_type == 'bar':
            shap.summary_plot(
                shap_values_to_plot, 
                X, 
                plot_type="bar", 
                max_display=max_display,
                feature_names=feature_names_to_use,
                show=False
            )
            plt.title(f"{model_name} - {translate('SHAP特征重要性')}")
        else:
            shap.summary_plot(
                shap_values_to_plot, 
                X, 
                max_display=max_display,
                feature_names=feature_names_to_use,
                show=False
            )
            plt.title(f"{model_name} - {translate('SHAP特征影响分布')}")
            
        # 添加图例说明
        if plot_type != 'bar':
            # 在图例下方添加说明文本
            plt.figtext(0.5, 0.01, "颜色表示特征值（红色=高，蓝色=低）\nSHAP值表示对预测的影响（正值=增加预测概率，负值=降低预测概率）", 
                       ha='center', fontsize=10, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        
        # 保存图形
        if output_dir is None:
            output_dir = CONFIG['output_path'] / model_name
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        save_format = PLOT_CONFIG.get('save_format', 'png')
        filepath = output_dir / f"{model_name}_shap_summary_{plot_type}.{save_format}"
        plt.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
        plt.close()
        
        logger.info(f"SHAP摘要图已保存至: {filepath}")
        return str(filepath)
    
    except Exception as e:
        logger.error(f"生成SHAP摘要图失败: {e}")
        import traceback
        traceback.print_exc()
        plt.close()
        return None

def plot_shap_dependence(model, X, feature, model_name='model', interaction_feature=None, output_dir=None):
    """
    生成SHAP依赖图
    
    Args:
        model: 训练好的模型
        X: 特征数据
        feature: 要分析的特征名称或索引
        model_name: 模型名称，用于保存文件
        interaction_feature: 交互特征名称或索引，如果为None则不显示交互
        output_dir: 输出目录，如果为None则使用默认目录
    
    Returns:
        str: 保存的文件路径
    """
    logger.info(f"为模型 {model_name} 生成特征 '{feature}' 的SHAP依赖图")
    
    # 确保X是DataFrame格式，如果是numpy数组则转换
    if isinstance(X, np.ndarray):
        import pandas as pd
        # 如果feature是字符串，则需要转换为索引
        if isinstance(feature, str):
            logger.warning(f"X是numpy数组，但feature是字符串 '{feature}'，将尝试使用索引")
            try:
                feature_idx = int(feature.replace('feature_', ''))
                feature = feature_idx
            except:
                logger.error(f"无法将特征名称 '{feature}' 转换为索引")
                return None
        
        # 创建默认特征名称
        feature_names = [f'feature_{i}' for i in range(X.shape[1])]
        X = pd.DataFrame(X, columns=feature_names)
        
        # 更新feature为字符串名称
        if isinstance(feature, int):
            if 0 <= feature < len(feature_names):
                feature = feature_names[feature]
            else:
                logger.error(f"特征索引 {feature} 超出范围 [0, {len(feature_names)-1}]")
                return None
    
    try:
        # 尝试使用TreeExplainer
        try:
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X)
        except Exception as e:
            logger.warning(f"TreeExplainer 失败: {e}, 回退到 KernelExplainer")
            explainer = shap.KernelExplainer(model.predict_proba, X)
            shap_values = explainer.shap_values(X)
        
        # 创建新的图形并绘制SHAP依赖图
        plt.figure(figsize=(10, 6))
        
        # 处理不同类型的shap_values
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            # 二分类问题，通常我们关注正类
            shap_values_to_plot = shap_values[..., 1] if shap_values.ndim == 3 else shap_values[1]
        else:
            shap_values_to_plot = shap_values
        
        # 绘制依赖图
        if interaction_feature is not None:
            shap.dependence_plot(
                feature, 
                shap_values_to_plot, 
                X, 
                interaction_index=interaction_feature,
                show=False
            )
        else:
            shap.dependence_plot(
                feature, 
                shap_values_to_plot, 
                X,
                show=False
            )
        
        # 设置标题
        if interaction_feature:
            plt.title(f"{translate('SHAP依赖图')}: {feature} (交互: {interaction_feature})")
        else:
            plt.title(f"{translate('SHAP依赖图')}: {feature}")
        
        plt.tight_layout()
        
        # 保存图形
        if output_dir is None:
            output_dir = CONFIG['output_path'] / model_name
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 处理特征名称中可能存在的非法文件名字符
        safe_feature_name = str(feature).replace('/', '_').replace('\\', '_').replace(':', '_')
        
        save_format = PLOT_CONFIG.get('save_format', 'png')
        if interaction_feature:
            safe_interaction_name = str(interaction_feature).replace('/', '_').replace('\\', '_').replace(':', '_')
            filepath = output_dir / f"{model_name}_shap_dependence_{safe_feature_name}_with_{safe_interaction_name}.{save_format}"
        else:
            filepath = output_dir / f"{model_name}_shap_dependence_{safe_feature_name}.{save_format}"
        
        plt.savefig(filepath, dpi=CONFIG['dpi'], bbox_inches='tight')
        plt.close()
        
        logger.info(f"SHAP依赖图已保存至: {filepath}")
        return str(filepath)
    
    except Exception as e:
        logger.error(f"生成SHAP依赖图失败: {e}")
        plt.close()
        return None

def plot_roc_curve(model, X, y, filename='ROC.pdf'):
    """绘制ROC曲线"""
    try:
        # 获取预测概率
        y_probs = model.predict_proba(X)[:, 1]
        
        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(y, y_probs)
        
        # 计算AUC值
        roc_auc = auc(fpr, tpr)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=CONFIG['figsize'])
        
        # 设置图形背景色（可选）
        fig.patch.set_facecolor(CONFIG['colors']['background'])
        ax.set_facecolor(CONFIG['colors']['background'])
        
        # 绘制ROC曲线
        ax.plot(fpr, tpr, color=CONFIG['colors']['primary'], 
                linewidth=CONFIG['line_width'], 
                label=f"{CONFIG['model_name']} (AUC = {roc_auc:.3f})")
        
        # 添加对角线参考
        ax.plot([0, 1], [0, 1], color='gray', linestyle='--', 
                linewidth=1.5, alpha=0.7, label="Random")
        
        # 找到约登指数最大点（最优阈值点）
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        
        # 标记最优点
        ax.scatter(fpr[optimal_idx], tpr[optimal_idx], 
                   s=100, color=CONFIG['colors']['highlight'], alpha=0.8, 
                   label=f"Optimal (t={optimal_threshold:.3f})")
        
        # 设置轴范围和标签
        ax.set_xlim([0.0, 1.0])
        ax.set_ylim([0.0, 1.05])
        
        # 应用样式
        title = "ROC Curve"
        subtitle = f"(Balanced Data, n={len(y)})"
        ax.set_title(f"{title} - {CONFIG['model_name']}\n{subtitle}", 
                     fontsize=CONFIG['fontsize']['title'])
        ax.set_xlabel("False Positive Rate (1 - Specificity)", 
                      fontsize=CONFIG['fontsize']['label'])
        ax.set_ylabel("True Positive Rate (Sensitivity)", 
                      fontsize=CONFIG['fontsize']['label'])
        
        # 设置网格
        ax.grid(True, alpha=CONFIG['grid_alpha'], linestyle='--', 
                color=CONFIG['colors']['grid'])
        
        # 去除顶部和右侧边框
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        # 设置刻度标签字体大小
        ax.tick_params(axis='both', labelsize=CONFIG['fontsize']['tick'])
        
        # 添加图例
        ax.legend(loc="lower right", fontsize=CONFIG['fontsize']['legend'])
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        save_plot(fig, filename, CONFIG['model_name'], 'roc')
        
        return fig, ax
    except Exception as e:
        logger.error(f"绘制ROC曲线失败: {e}")
        logger.exception(e)
        return None, None

def plot_learning_curve(model, X, y, cv=5, filename='学习曲线.pdf'):
    """绘制学习曲线"""
    try:
        # 计算学习曲线
        train_sizes, train_scores, test_scores = learning_curve(
            model, X, y, cv=cv, scoring='roc_auc',
            train_sizes=np.linspace(0.1, 1.0, 10),
            n_jobs=-1
        )
        
        # 计算均值和标准差
        train_mean = np.mean(train_scores, axis=1)
        train_std = np.std(train_scores, axis=1)
        test_mean = np.mean(test_scores, axis=1)
        test_std = np.std(test_scores, axis=1)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=CONFIG['figsize'])
        
        # 设置图形背景色
        fig.patch.set_facecolor(CONFIG['colors']['background'])
        ax.set_facecolor(CONFIG['colors']['background'])
        
        # 填充标准差区域
        ax.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                       alpha=0.1, color=CONFIG['colors']['primary'])
        ax.fill_between(train_sizes, test_mean - test_std, test_mean + test_std, 
                       alpha=0.1, color=CONFIG['colors']['secondary'])
        
        # 绘制均值线
        ax.plot(train_sizes, train_mean, 'o-', 
               color=CONFIG['colors']['primary'], 
               markersize=CONFIG['marker_size'], 
               linewidth=CONFIG['line_width'], 
               label="Training Set")
        ax.plot(train_sizes, test_mean, 'o-', 
               color=CONFIG['colors']['secondary'], 
               markersize=CONFIG['marker_size'], 
               linewidth=CONFIG['line_width'], 
               label="Validation Set")
        
        # 设置Y轴范围以便更好地展示差异
        ax.set_ylim([0.8, 1.01])
        
        # 标记性能指标
        final_train = train_mean[-1]
        final_test = test_mean[-1]
        gap = final_train - final_test
        
        # 添加性能注释框
        perf_text = f"Training AUC: {final_train:.3f}\nValidation AUC: {final_test:.3f}\nGap: {gap:.3f}"
        
        # 根据差距评估模型
        if gap < 0.05:
            evaluation = "Good model performance"
            box_color = "lightgreen"
        elif gap < 0.1:
            evaluation = "Slight overfitting"
            box_color = "khaki"
        else:
            evaluation = "Significant overfitting"
            box_color = "lightcoral"
        
        # 添加评估注释
        bbox_props = dict(boxstyle="round,pad=0.5", facecolor=box_color, alpha=0.7)
        ax.text(0.05, 0.95, perf_text + "\n\n" + evaluation, transform=ax.transAxes, 
               fontsize=CONFIG['fontsize']['annotation'], verticalalignment='top', bbox=bbox_props)
        
        # 应用样式
        ax.set_title(f"Learning Curve - {CONFIG['model_name']}", 
                     fontsize=CONFIG['fontsize']['title'])
        ax.set_xlabel("Training Examples", fontsize=CONFIG['fontsize']['label'])
        ax.set_ylabel("AUC Score", fontsize=CONFIG['fontsize']['label'])
        
        # 设置网格
        ax.grid(True, alpha=CONFIG['grid_alpha'], linestyle='--', 
                color=CONFIG['colors']['grid'])
        
        # 去除顶部和右侧边框
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        
        # 设置刻度标签字体大小
        ax.tick_params(axis='both', labelsize=CONFIG['fontsize']['tick'])
        
        # 添加图例
        ax.legend(loc="lower right", fontsize=CONFIG['fontsize']['legend'])
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        save_plot(fig, filename, CONFIG['model_name'], 'learning_curve')
        
        return fig, ax
    except Exception as e:
        logger.error(f"绘制学习曲线失败: {e}")
        logger.exception(e)
        return None, None
